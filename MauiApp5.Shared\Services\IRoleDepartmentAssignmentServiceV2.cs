using MauiApp5.Shared.Models.Database;
using MauiApp5.Shared.Data;
using Microsoft.Extensions.Logging;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 角色部门分配服务接口
    /// 重新设计的简化版本
    /// </summary>
    public interface IRoleDepartmentAssignmentServiceV2
    {
        #region 查询操作

        /// <summary>
        /// 获取所有角色的部门分配视图模型
        /// </summary>
        Task<List<RoleDepartmentAssignmentViewModel>> GetAllRoleAssignmentsAsync();

        /// <summary>
        /// 获取指定角色的部门分配视图模型
        /// </summary>
        Task<RoleDepartmentAssignmentViewModel?> GetRoleAssignmentAsync(int roleId);

        /// <summary>
        /// 获取分配统计信息
        /// </summary>
        Task<AssignmentStatistics> GetAssignmentStatisticsAsync();

        /// <summary>
        /// 检查角色是否分配了指定部门
        /// </summary>
        Task<bool> IsRoleAssignedToDepartmentAsync(int roleId, int departmentId);

        /// <summary>
        /// 获取用户可访问的部门列表（通过角色分配）
        /// </summary>
        Task<List<Department>> GetUserAccessibleDepartmentsAsync(int userId);

        #endregion

        #region 分配操作

        /// <summary>
        /// 为角色分配部门
        /// </summary>
        Task<bool> AssignDepartmentToRoleAsync(int roleId, int departmentId, int? operatorId = null);

        /// <summary>
        /// 取消角色的部门分配
        /// </summary>
        Task<bool> UnassignDepartmentFromRoleAsync(int roleId, int departmentId);

        /// <summary>
        /// 批量设置角色的部门分配（替换现有分配）
        /// </summary>
        Task<bool> SetRoleAssignmentsAsync(int roleId, List<int> departmentIds, int? operatorId = null);

        /// <summary>
        /// 清空角色的所有部门分配
        /// </summary>
        Task<bool> ClearRoleAssignmentsAsync(int roleId);

        /// <summary>
        /// 切换角色部门分配状态
        /// </summary>
        Task<bool> ToggleAssignmentAsync(int roleId, int departmentId, int? operatorId = null);

        #endregion

        #region 权限验证

        /// <summary>
        /// 检查用户是否可以访问指定部门
        /// </summary>
        Task<bool> CanUserAccessDepartmentAsync(int userId, int departmentId);

        /// <summary>
        /// 获取用户可以报修的部门列表
        /// </summary>
        Task<List<Department>> GetUserReportableDepartmentsAsync(int userId);

        #endregion
    }

    /// <summary>
    /// 角色部门分配服务实现
    /// </summary>
    public class RoleDepartmentAssignmentServiceV2 : IRoleDepartmentAssignmentServiceV2
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<RoleDepartmentAssignmentServiceV2> _logger;

        public RoleDepartmentAssignmentServiceV2(
            DatabaseContext dbContext,
            ILogger<RoleDepartmentAssignmentServiceV2> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 查询操作

        public async Task<List<RoleDepartmentAssignmentViewModel>> GetAllRoleAssignmentsAsync()
        {
            try
            {
                // 获取所有角色
                var roles = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.IsEnabled)
                    .OrderBy(r => r.SortOrder)
                    .ToListAsync();

                // 获取所有部门
                var departments = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.IsEnabled)
                    .OrderBy(d => d.SortOrder)
                    .ToListAsync();

                // 获取所有分配关系
                var assignments = await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .Where(rda => rda.IsEnabled)
                    .ToListAsync();

                // 构建视图模型
                var result = new List<RoleDepartmentAssignmentViewModel>();

                foreach (var role in roles)
                {
                    var roleAssignments = assignments.Where(a => a.RoleId == role.Id).ToList();

                    var viewModel = new RoleDepartmentAssignmentViewModel
                    {
                        RoleId = role.Id,
                        RoleName = role.Name,
                        RoleCode = role.Code,
                        Departments = departments.Select(dept => new DepartmentAssignmentItem
                        {
                            DepartmentId = dept.Id,
                            DepartmentName = dept.Name,
                            DepartmentCode = dept.Code,
                            IsAssigned = roleAssignments.Any(a => a.DepartmentId == dept.Id),
                            AssignedAt = roleAssignments.FirstOrDefault(a => a.DepartmentId == dept.Id)?.CreatedAt
                        }).ToList()
                    };

                    result.Add(viewModel);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有角色分配失败");
                return new List<RoleDepartmentAssignmentViewModel>();
            }
        }

        public async Task<RoleDepartmentAssignmentViewModel?> GetRoleAssignmentAsync(int roleId)
        {
            try
            {
                var allAssignments = await GetAllRoleAssignmentsAsync();
                return allAssignments.FirstOrDefault(a => a.RoleId == roleId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取角色分配失败：{roleId}", roleId);
                return null;
            }
        }

        public async Task<AssignmentStatistics> GetAssignmentStatisticsAsync()
        {
            try
            {
                var totalRoles = await _dbContext.Db.Queryable<Role>()
                    .Where(r => r.IsEnabled)
                    .CountAsync();

                var totalDepartments = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.IsEnabled)
                    .CountAsync();

                var totalAssignments = await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .Where(rda => rda.IsEnabled)
                    .CountAsync();

                var rolesWithAssignments = await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .Where(rda => rda.IsEnabled)
                    .Select(rda => rda.RoleId)
                    .Distinct()
                    .CountAsync();

                return new AssignmentStatistics
                {
                    TotalRoles = totalRoles,
                    TotalDepartments = totalDepartments,
                    TotalAssignments = totalAssignments,
                    RolesWithAssignments = rolesWithAssignments
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取分配统计失败");
                return new AssignmentStatistics();
            }
        }

        public async Task<bool> IsRoleAssignedToDepartmentAsync(int roleId, int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .Where(rda => rda.RoleId == roleId && rda.DepartmentId == departmentId && rda.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查角色部门分配失败：{roleId}, {departmentId}", roleId, departmentId);
                return false;
            }
        }

        public async Task<List<Department>> GetUserAccessibleDepartmentsAsync(int userId)
        {
            try
            {
                // 通过用户的角色获取可访问的部门
                var departmentIds = await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .InnerJoin<UserRole>((rda, ur) => rda.RoleId == ur.RoleId)
                    .InnerJoin<User>((rda, ur, u) => ur.UserId == u.Id)
                    .Where((rda, ur, u) => u.Id == userId && rda.IsEnabled && u.IsEnabled && ur.IsEnabled)
                    .Select((rda, ur, u) => rda.DepartmentId)
                    .Distinct()
                    .ToListAsync();

                if (!departmentIds.Any())
                    return new List<Department>();

                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => departmentIds.Contains(d.Id) && d.IsEnabled)
                    .OrderBy(d => d.SortOrder)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可访问部门失败：{userId}", userId);
                return new List<Department>();
            }
        }

        #endregion

        #region 分配操作

        public async Task<bool> AssignDepartmentToRoleAsync(int roleId, int departmentId, int? operatorId = null)
        {
            try
            {
                // 检查是否已存在
                var exists = await IsRoleAssignedToDepartmentAsync(roleId, departmentId);
                if (exists)
                {
                    _logger.LogInformation("角色部门分配已存在：{roleId} -> {departmentId}", roleId, departmentId);
                    return true;
                }

                var assignment = new RoleDepartmentAssignment
                {
                    RoleId = roleId,
                    DepartmentId = departmentId,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now,
                    CreatedBy = operatorId
                };

                var result = await _dbContext.Db.Insertable(assignment).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("角色部门分配成功：{roleId} -> {departmentId}", roleId, departmentId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配部门到角色失败：{roleId} -> {departmentId}", roleId, departmentId);
                return false;
            }
        }

        public async Task<bool> UnassignDepartmentFromRoleAsync(int roleId, int departmentId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<RoleDepartmentAssignment>()
                    .Where(rda => rda.RoleId == roleId && rda.DepartmentId == departmentId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("取消角色部门分配：{roleId} -> {departmentId}, 删除了{count}条记录",
                    roleId, departmentId, result);

                return result >= 0; // 即使没有删除记录也认为成功
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消角色部门分配失败：{roleId} -> {departmentId}", roleId, departmentId);
                return false;
            }
        }

        public async Task<bool> SetRoleAssignmentsAsync(int roleId, List<int> departmentIds, int? operatorId = null)
        {
            try
            {
                _logger.LogInformation("开始设置角色分配：{roleId} -> [{departmentIds}]",
                    roleId, string.Join(",", departmentIds));

                // 使用事务确保数据一致性
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 删除现有分配
                    await _dbContext.Db.Deleteable<RoleDepartmentAssignment>()
                        .Where(rda => rda.RoleId == roleId)
                        .ExecuteCommandAsync();

                    // 2. 插入新分配
                    if (departmentIds.Any())
                    {
                        var assignments = departmentIds.Select(deptId => new RoleDepartmentAssignment
                        {
                            RoleId = roleId,
                            DepartmentId = deptId,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now,
                            CreatedBy = operatorId
                        }).ToList();

                        await _dbContext.Db.Insertable(assignments).ExecuteCommandAsync();
                    }
                });

                _logger.LogInformation("角色分配设置完成：{roleId}", roleId);
                return result.IsSuccess;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置角色分配失败：{roleId}", roleId);
                return false;
            }
        }

        public async Task<bool> ClearRoleAssignmentsAsync(int roleId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<RoleDepartmentAssignment>()
                    .Where(rda => rda.RoleId == roleId)
                    .ExecuteCommandAsync();

                _logger.LogInformation("清空角色分配：{roleId}, 删除了{count}条记录", roleId, result);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空角色分配失败：{roleId}", roleId);
                return false;
            }
        }

        public async Task<bool> ToggleAssignmentAsync(int roleId, int departmentId, int? operatorId = null)
        {
            try
            {
                var isAssigned = await IsRoleAssignedToDepartmentAsync(roleId, departmentId);

                if (isAssigned)
                {
                    return await UnassignDepartmentFromRoleAsync(roleId, departmentId);
                }
                else
                {
                    return await AssignDepartmentToRoleAsync(roleId, departmentId, operatorId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换分配状态失败：{roleId} -> {departmentId}", roleId, departmentId);
                return false;
            }
        }

        #endregion

        #region 权限验证

        public async Task<bool> CanUserAccessDepartmentAsync(int userId, int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RoleDepartmentAssignment>()
                    .InnerJoin<UserRole>((rda, ur) => rda.RoleId == ur.RoleId)
                    .InnerJoin<User>((rda, ur, u) => ur.UserId == u.Id)
                    .Where((rda, ur, u) => u.Id == userId &&
                                          rda.DepartmentId == departmentId &&
                                          rda.IsEnabled &&
                                          u.IsEnabled &&
                                          ur.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户部门访问权限失败：{userId} -> {departmentId}", userId, departmentId);
                return false;
            }
        }

        public async Task<List<Department>> GetUserReportableDepartmentsAsync(int userId)
        {
            // 对于报修功能，用户可以访问的部门就是可以报修的部门
            return await GetUserAccessibleDepartmentsAsync(userId);
        }

        #endregion
    }
}
