@page "/test-user-data"
@using MauiApp5.Shared.Data
@using MauiApp5.Shared.Models.Database
@inject DatabaseContext DbContext

<PageTitle>测试用户数据</PageTitle>

<h3>测试用户数据</h3>

<div style="margin-bottom: 20px;">
    <button @onclick="TestDatabaseConnection" class="btn btn-primary">测试数据库连接</button>
    <button @onclick="TestDirectQuery" class="btn btn-secondary" style="margin-left: 10px;">直接查询用户</button>
    <button @onclick="TestUserManagementService" class="btn btn-info" style="margin-left: 10px;">测试用户管理服务</button>
</div>

@if (!string.IsNullOrEmpty(message))
{
    <div class="alert alert-info">
        <pre>@message</pre>
    </div>
}

@if (users.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>显示名称</th>
                <th>邮箱</th>
                <th>密码</th>
                <th>状态</th>
                <th>创建时间</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var user in users)
            {
                <tr>
                    <td>@user.Id</td>
                    <td>@user.Username</td>
                    <td>@user.DisplayName</td>
                    <td>@user.Email</td>
                    <td style="font-family: monospace; color: #666;">@user.PasswordHash</td>
                    <td>
                        <span class="badge @(user.IsEnabled ? "bg-success" : "bg-danger")">
                            @(user.IsEnabled ? "启用" : "禁用")
                        </span>
                        @if (user.IsLocked)
                        {
                            <span class="badge bg-warning">锁定</span>
                        }
                    </td>
                    <td>@user.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")</td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    private List<User> users = new();
    private string message = "";

    private async Task TestDatabaseConnection()
    {
        try
        {
            message = "正在测试数据库连接...\n";
            StateHasChanged();

            // 测试数据库连接
            var connectionString = DbContext.Db.CurrentConnectionConfig.ConnectionString;
            message += $"连接字符串: {connectionString}\n";

            // 测试简单查询
            var result = await DbContext.Db.Ado.GetDataTableAsync("SELECT @@VERSION as Version");
            if (result.Rows.Count > 0)
            {
                message += $"数据库版本: {result.Rows[0]["Version"]}\n";
            }

            // 检查用户表是否存在
            var tableExists = await DbContext.Db.Ado.GetDataTableAsync("SELECT COUNT(*) as Count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users'");
            var count = Convert.ToInt32(tableExists.Rows[0]["Count"]);
            message += $"用户表是否存在: {(count > 0 ? "是" : "否")}\n";

            if (count > 0)
            {
                // 检查用户表记录数
                var userCount = await DbContext.Db.Ado.GetDataTableAsync("SELECT COUNT(*) as Count FROM Users");
                var userCountValue = Convert.ToInt32(userCount.Rows[0]["Count"]);
                message += $"用户表记录数: {userCountValue}\n";
            }

            message += "数据库连接测试完成！";
        }
        catch (Exception ex)
        {
            message += $"数据库连接测试失败: {ex.Message}\n{ex.StackTrace}";
        }
    }

    private async Task TestDirectQuery()
    {
        try
        {
            message = "正在直接查询用户数据...\n";
            StateHasChanged();

            // 直接使用SqlSugar查询
            users = await DbContext.Db.Queryable<User>().ToListAsync();
            message += $"查询到 {users.Count} 个用户\n";

            foreach (var user in users)
            {
                message += $"用户: {user.Username} - {user.DisplayName} - {user.Email} - 启用:{user.IsEnabled} - 锁定:{user.IsLocked}\n";
            }

            message += "直接查询完成！";
        }
        catch (Exception ex)
        {
            message += $"直接查询失败: {ex.Message}\n{ex.StackTrace}";
        }
    }

    private async Task TestUserManagementService()
    {
        try
        {
            message = "正在测试用户管理服务...\n";
            StateHasChanged();

            // 这里我们需要手动创建服务实例来测试
            var logger = new Microsoft.Extensions.Logging.Abstractions.NullLogger<MauiApp5.Shared.Services.UserManagementService>();
            var userService = new MauiApp5.Shared.Services.UserManagementService(DbContext, logger);

            var result = await userService.GetUsersAsync(1, 100);
            users = result.Users;
            message += $"用户管理服务查询到 {result.Users.Count} 个用户，总数: {result.TotalCount}\n";

            foreach (var user in result.Users)
            {
                message += $"用户: {user.Username} - {user.DisplayName} - {user.Email}\n";
            }

            message += "用户管理服务测试完成！";
        }
        catch (Exception ex)
        {
            message += $"用户管理服务测试失败: {ex.Message}\n{ex.StackTrace}";
        }
    }
} 