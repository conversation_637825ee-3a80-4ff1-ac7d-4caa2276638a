namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 认证状态存储接口 - 用于不同平台的持久化实现
    /// </summary>
    public interface IAuthenticationStateStorage
    {
        /// <summary>
        /// 保存认证状态
        /// </summary>
        Task SaveAuthStateAsync(string key, string data);

        /// <summary>
        /// 获取认证状态
        /// </summary>
        Task<string?> GetAuthStateAsync(string key);

        /// <summary>
        /// 清除认证状态
        /// </summary>
        Task ClearAuthStateAsync(string key);

        /// <summary>
        /// 检查存储是否可用
        /// </summary>
        bool IsAvailable { get; }
    }
} 