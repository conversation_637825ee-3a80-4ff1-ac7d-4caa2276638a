using MauiApp5.Shared.Services;

namespace MauiApp5.Platforms.Windows
{
    public class NotificationService : INotificationService
    {
        public async Task<bool> RequestPermissionAsync()
        {
            // Windows 10/11 通常不需要显式权限请求
            return await Task.FromResult(true);
        }

        public async Task SendNotificationAsync(string title, string message)
        {
            try
            {
                // 在Windows上显示系统托盘气球提示
                // 这是一个简单但有效的通知方式
                var notificationTitle = title ?? "通知";
                var notificationMessage = message ?? "消息";

                // 使用MessageBox作为简单的通知方式（在实际应用中可以替换为更好的实现）
                if (Application.Current?.Dispatcher != null && Application.Current?.MainPage != null)
                {
                    await Application.Current.Dispatcher.DispatchAsync(async () =>
                    {
                        // 在主线程上显示通知
                        if (Application.Current?.MainPage != null)
                        {
                            await Application.Current.MainPage.DisplayAlert(notificationTitle, notificationMessage, "确定");
                        }
                    });
                }
                else
                {
                    // 如果无法访问UI，则输出到调试
                    System.Diagnostics.Debug.WriteLine($"Windows通知(无UI): {notificationTitle} - {notificationMessage}");
                }

                System.Diagnostics.Debug.WriteLine($"Windows通知已显示: {title} - {message}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Windows通知发送失败: {ex.Message}");
                // 回退到调试输出
                System.Diagnostics.Debug.WriteLine($"Windows通知(回退): {title} - {message}");
            }

            await Task.CompletedTask;
        }
    }
} 