using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 角色部门权限表
    /// </summary>
    [SugarTable("RoleDepartmentPermissions")]
    public class RoleDepartmentPermission
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "角色ID不能为空")]
        public int RoleId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "部门ID不能为空")]
        public int DepartmentId { get; set; }

        /// <summary>
        /// 权限类型：1=可报修设备, 2=可接收报修, 3=可维修设备
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "权限类型不能为空")]
        [Range(1, 3, ErrorMessage = "权限类型必须在1-3之间")]
        public int PermissionType { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的角色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Role? Role { get; set; }

        /// <summary>
        /// 关联的部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Department { get; set; }

        /// <summary>
        /// 权限类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string PermissionTypeName => PermissionType switch
        {
            1 => "可报修设备",
            2 => "可接收报修",
            3 => "可维修设备",
            _ => "未知"
        };
    }

    /// <summary>
    /// 权限类型枚举
    /// </summary>
    public enum RoleDepartmentPermissionType
    {
        /// <summary>
        /// 可报修设备
        /// </summary>
        CanReportEquipment = 1,

        /// <summary>
        /// 可接收报修
        /// </summary>
        CanReceiveRepair = 2,

        /// <summary>
        /// 可维修设备
        /// </summary>
        CanMaintainEquipment = 3
    }
}
