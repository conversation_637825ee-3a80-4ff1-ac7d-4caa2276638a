using SqlSugar;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 用户直接权限关联实体
    /// </summary>
    [SugarTable("UserPermissions")]
    public class UserPermission
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int UserId { get; set; }

        /// <summary>
        /// 权限ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int PermissionId { get; set; }

        /// <summary>
        /// 权限类型（1=授权，0=拒绝）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsGranted { get; set; } = true;

        /// <summary>
        /// 分配时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime AssignedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 分配人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AssignedBy { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 过期时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User User { get; set; } = null!;

        /// <summary>
        /// 权限
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Permission Permission { get; set; } = null!;
    }
} 