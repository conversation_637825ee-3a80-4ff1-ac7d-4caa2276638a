@page "/devicerepair/"
@page "/devicerepair/{DeviceCode}"
@inject NavigationManager Navigation
@using System.ComponentModel.DataAnnotations
@using MauiApp5.Shared.Components

<PageTitle>设备报修</PageTitle>

<PermissionView RequiredPermission="DeviceRepair.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <div class="d-flex justify-space-between align-center mb-4">
                <MudText Typo="Typo.h3">设备报修</MudText>
                <MudButton Variant="Variant.Outlined" 
                          OnClick="GoBack"
                          StartIcon="@Icons.Material.Filled.ArrowBack">
                    返回扫描页面
                </MudButton>
            </div>

            @if (!string.IsNullOrEmpty(DeviceCode))
            {
    <!-- 设备信息卡片 -->
                <MudPaper Class="pa-4 mb-4">
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="@Icons.Material.Filled.Search" 
                                Color="Color.Primary" 
                                Class="mr-2" />
                        <MudText Typo="Typo.h5">设备信息</MudText>
                    </div>
                    
                    <MudGrid>
                        <MudItem xs="12" md="6">
                            <MudText Typo="Typo.body1" Class="mb-2">
                                <strong>设备编号:</strong> @DeviceCode
                            </MudText>
                            <MudText Typo="Typo.body1" Class="mb-2">
                                <strong>设备名称:</strong> @deviceInfo.DeviceName
                            </MudText>
                            <MudText Typo="Typo.body1" Class="mb-2">
                                <strong>设备型号:</strong> @deviceInfo.DeviceModel
                            </MudText>
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudText Typo="Typo.body1" Class="mb-2">
                                <strong>所在位置:</strong> @deviceInfo.Location
                            </MudText>
                            <MudText Typo="Typo.body1" Class="mb-2">
                                <strong>负责人:</strong> @deviceInfo.Responsible
                            </MudText>
                            <MudText Typo="Typo.body1" Class="mb-2">
                    <strong>设备状态:</strong>
                                <MudChip T="string" 
                                        Size="Size.Small" 
                                        Color="@GetDeviceStatusColor(deviceInfo.Status)">
                                    @deviceInfo.Status
                                </MudChip>
                            </MudText>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

    <!-- 报修表单 -->
                <MudPaper Class="pa-4 mb-4">
                    <div class="d-flex align-center mb-3">
                        <MudIcon Icon="@Icons.Material.Filled.Build" 
                                Color="Color.Primary" 
                                Class="mr-2" />
                        <MudText Typo="Typo.h5">报修信息</MudText>
            </div>

                    <EditForm Model="@repairForm" OnValidSubmit="SubmitRepair">
                        <DataAnnotationsValidator />
                        
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudSelect T="string" 
                                          @bind-Value="repairForm.FaultType" 
                                          Label="故障类型" 
                                          Variant="Variant.Outlined"
                                          Required="true">
                                    <MudSelectItem Value="@("硬件故障")">硬件故障</MudSelectItem>
                                    <MudSelectItem Value="@("软件故障")">软件故障</MudSelectItem>
                                    <MudSelectItem Value="@("网络故障")">网络故障</MudSelectItem>
                                    <MudSelectItem Value="@("其他故障")">其他故障</MudSelectItem>
                                </MudSelect>
                                <ValidationMessage For="@(() => repairForm.FaultType)" />
                            </MudItem>
                            
                            <MudItem xs="12" md="6">
                                <MudSelect T="string" 
                                          @bind-Value="repairForm.Priority" 
                                          Label="紧急程度" 
                                          Variant="Variant.Outlined"
                                          Required="true">
                                    <MudSelectItem Value="@("低")">低</MudSelectItem>
                                    <MudSelectItem Value="@("中")">中</MudSelectItem>
                                    <MudSelectItem Value="@("高")">高</MudSelectItem>
                                    <MudSelectItem Value="@("紧急")">紧急</MudSelectItem>
                                </MudSelect>
                                <ValidationMessage For="@(() => repairForm.Priority)" />
                            </MudItem>
                            
                            <MudItem xs="12">
                                <MudTextField @bind-Value="repairForm.Description"
                                             Label="故障描述"
                                             Variant="Variant.Outlined"
                                             Lines="4"
                                             Placeholder="请详细描述设备故障现象..."
                                             Required="true" />
                                <ValidationMessage For="@(() => repairForm.Description)" />
                            </MudItem>
                            
                            <MudItem xs="12" md="6">
                                <MudTextField @bind-Value="repairForm.ReporterName"
                                             Label="报修人姓名"
                                             Variant="Variant.Outlined"
                                             Required="true" />
                                <ValidationMessage For="@(() => repairForm.ReporterName)" />
                            </MudItem>
                            
                            <MudItem xs="12" md="6">
                                <MudTextField @bind-Value="repairForm.ContactPhone"
                                             Label="联系电话"
                                             Variant="Variant.Outlined"
                                             Required="true" />
                                <ValidationMessage For="@(() => repairForm.ContactPhone)" />
                            </MudItem>
                        </MudGrid>
                        
                        <div class="d-flex gap-2 mt-4">
                            <MudButton ButtonType="ButtonType.Submit" 
                                      Variant="Variant.Filled" 
                                      Color="Color.Primary"
                                      StartIcon="@Icons.Material.Filled.Send"
                                      Disabled="isSubmitting">
                                @(isSubmitting ? "提交中..." : "提交报修")
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                 OnClick="ResetForm"
                                      StartIcon="@Icons.Material.Filled.Refresh">
                                重置表单
                            </MudButton>
                        </div>
                    </EditForm>
                </MudPaper>

                <!-- 历史报修记录 -->
                <PermissionView RequiredPermission="Device.Repair.ViewHistory">
                    <ChildContent>
                        <MudPaper Class="pa-4">
                            <div class="d-flex align-center mb-3">
                                <MudIcon Icon="@Icons.Material.Filled.History" 
                                        Color="Color.Primary" 
                                        Class="mr-2" />
                                <MudText Typo="Typo.h5">历史报修记录</MudText>
            </div>

                            @if (repairHistory.Any())
                            {
                                <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                                    @foreach (var record in repairHistory.Take(5))
                                    {
                                        <MudTimelineItem>
                                            <ItemOpposite>
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    @record.CreateTime.ToString("yyyy-MM-dd")
                                                </MudText>
                                            </ItemOpposite>
                                            <ItemContent>
                                                <MudPaper Class="pa-3" Elevation="2">
                                                    <div class="d-flex justify-space-between align-center mb-2">
                                                        <MudText Typo="Typo.subtitle1">@record.FaultType</MudText>
                                                        <MudChip T="string" 
                                                                Size="Size.Small" 
                                                                Color="@GetStatusColor(record.Status)">
                                                            @record.Status
                                                        </MudChip>
        </div>
                                                    <MudText Typo="Typo.body2" Class="mb-1">@record.Description</MudText>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                        报修人: @record.ReporterName | 优先级: @record.Priority
                                                    </MudText>
                                                </MudPaper>
                                            </ItemContent>
                                        </MudTimelineItem>
                                    }
                                </MudTimeline>
    }
    else
    {
                                <MudAlert Severity="Severity.Info">
                                    该设备暂无历史报修记录
                                </MudAlert>
                            }
                        </MudPaper>
                    </ChildContent>
                </PermissionView>
            }
            else
            {
                <!-- 无设备编号提示 -->
                <MudPaper Class="pa-6 text-center" Style="min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" 
                            Size="Size.Large" 
                            Color="Color.Warning" 
                            Class="mb-3" />
                    <MudText Typo="Typo.h4" Class="mb-2">请扫描设备二维码</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">使用设备扫描功能获取设备编号后再进行报修</MudText>
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              OnClick="GoToScanner"
                              StartIcon="@Icons.Material.Filled.QrCodeScanner">
                        前往扫描页面
                    </MudButton>
                </MudPaper>
            }
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudPaper Class="pa-6 text-center" Style="min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
            <MudIcon Icon="@Icons.Material.Filled.Lock" 
                    Size="Size.Large" 
                    Color="Color.Warning" 
                    Class="mb-3" />
            <MudText Typo="Typo.h4" Class="mb-2">权限不足</MudText>
            <MudText Typo="Typo.body1" Class="mb-4">您没有设备报修权限，请联系管理员</MudText>
            <MudButton Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      OnClick="GoBack"
                      StartIcon="@Icons.Material.Filled.ArrowBack">
                返回
            </MudButton>
        </MudPaper>
    </NotAuthorized>
</PermissionView>

@code {
    [Parameter] public string? DeviceCode { get; set; }
    
    private DeviceInfo deviceInfo = new();
    private RepairForm repairForm = new();
    private List<RepairRecord> repairHistory = new();
    private string statusMessage = "";
    private bool isSuccess = false;
    private bool isSubmitting = false;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(DeviceCode))
        {
            await LoadDeviceInfo();
            await LoadRepairHistory();
            InitializeForm();
        }
    }

    private async Task LoadDeviceInfo()
    {
        try
        {
            // 模拟从数据库或API加载设备信息
            await Task.Delay(300);
            
            deviceInfo = new DeviceInfo
            {
                DeviceCode = DeviceCode!,
                DeviceName = GetDeviceNameByCode(DeviceCode!),
                DeviceModel = "Model-X1",
                Location = "生产线A区",
                Responsible = "张三",
                Status = "运行中"
            };
        }
        catch (Exception ex)
        {
            SetStatus($"加载设备信息失败: {ex.Message}", false);
        }
    }

    private async Task LoadRepairHistory()
    {
        try
        {
            // 模拟加载历史记录
            await Task.Delay(200);
            
            repairHistory = new List<RepairRecord>
            {
                new RepairRecord
                {
                    FaultType = "网络故障",
                    Description = "设备无法连接到生产管理系统",
                    Priority = "高",
                    Status = "已完成",
                    ReporterName = "李四",
                    CreateTime = DateTime.Now.AddDays(-7)
                },
                new RepairRecord
                {
                    FaultType = "硬件故障",
                    Description = "传感器读数异常",
                    Priority = "中",
                    Status = "处理中",
                    ReporterName = "王五",
                    CreateTime = DateTime.Now.AddDays(-3)
                }
            };
        }
        catch (Exception ex)
        {
            SetStatus($"加载历史记录失败: {ex.Message}", false);
        }
    }

    private void InitializeForm()
    {
        repairForm = new RepairForm
        {
            DeviceCode = DeviceCode!,
            ReportTime = DateTime.Now
        };
    }

    private async Task SubmitRepair()
    {
        try
        {
            isSubmitting = true;
            StateHasChanged();

            // 模拟提交报修
            await Task.Delay(1500);
            
            // 添加到历史记录
            var newRecord = new RepairRecord
            {
                FaultType = repairForm.FaultType!,
                Description = repairForm.Description!,
                Priority = repairForm.Priority!,
                Status = "待处理",
                ReporterName = repairForm.ReporterName!,
                CreateTime = DateTime.Now
            };
            repairHistory.Insert(0, newRecord);

            SetStatus("报修提交成功！工单号: " + GenerateWorkOrderNumber(), true);
            ResetForm();
        }
        catch (Exception ex)
        {
            SetStatus($"提交报修失败: {ex.Message}", false);
        }
        finally
        {
            isSubmitting = false;
            StateHasChanged();
        }
    }

    private void ResetForm()
    {
        InitializeForm();
        StateHasChanged();
    }

    private string GetDeviceNameByCode(string code)
    {
        return code switch
        {
            "FN-DX-0001" => "数控机床A1",
            "FN-DX-0002" => "数控机床A2",
            "TEST-001" => "测试设备1",
            _ => "未知设备"
        };
    }

    private Color GetDeviceStatusColor(string status)
    {
        return status switch
        {
            "运行中" => Color.Success,
            "故障" => Color.Error,
            "维护中" => Color.Warning,
            _ => Color.Default
        };
    }

    private Color GetStatusColor(string status)
    {
        return status switch
        {
            "已完成" => Color.Success,
            "处理中" => Color.Info,
            "待处理" => Color.Warning,
            "已取消" => Color.Default,
            _ => Color.Default
        };
    }

    private string GenerateWorkOrderNumber()
    {
        return $"WO{DateTime.Now:yyyyMMddHHmmss}";
    }

    private void SetStatus(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
        StateHasChanged();
        
        // 5秒后自动清除状态消息
        _ = Task.Delay(5000).ContinueWith(_ => InvokeAsync(ClearStatus));
    }
    
    private void ClearStatus()
    {
        statusMessage = "";
        StateHasChanged();
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/devicescanner");
    }

    private void GoToScanner()
    {
        Navigation.NavigateTo("/devicescanner");
    }

    // 数据模型
    public class DeviceInfo
    {
        public string DeviceCode { get; set; } = "";
        public string DeviceName { get; set; } = "";
        public string DeviceModel { get; set; } = "";
        public string Location { get; set; } = "";
        public string Responsible { get; set; } = "";
        public string Status { get; set; } = "";
    }

    public class RepairForm
    {
        [Required(ErrorMessage = "请选择故障类型")]
        public string? FaultType { get; set; }

        [Required(ErrorMessage = "请选择紧急程度")]
        public string? Priority { get; set; }

        [Required(ErrorMessage = "请描述故障现象")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "请输入报修人姓名")]
        public string? ReporterName { get; set; }

        [Required(ErrorMessage = "请输入联系电话")]
        public string? ContactPhone { get; set; }

        public string DeviceCode { get; set; } = "";
        public DateTime ReportTime { get; set; }
    }

    public class RepairRecord
    {
        public string FaultType { get; set; } = "";
        public string Description { get; set; } = "";
        public string Priority { get; set; } = "";
        public string Status { get; set; } = "";
        public string ReporterName { get; set; } = "";
        public DateTime CreateTime { get; set; }
    }
} 