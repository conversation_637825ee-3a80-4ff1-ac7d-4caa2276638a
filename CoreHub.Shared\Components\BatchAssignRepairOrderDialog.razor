@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services

@inject IMaintenanceDashboardService MaintenanceDashboardService
@inject IRepairOrderService RepairOrderService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 700px;">
            <MudText Typo="Typo.h6" Class="mb-4">
                批量分配报修单
            </MudText>

            <!-- 选中的报修单列表 -->
            <MudCard Class="mb-4">
                <MudCardContent>
                    <MudText Typo="Typo.subtitle1" Class="mb-2">
                        选中的报修单 (@SelectedRepairOrders.Count 个)
                    </MudText>
                    <MudList T="string" Dense="true" Style="max-height: 200px; overflow-y: auto;">
                        @foreach (var repairOrder in SelectedRepairOrders.Take(10))
                        {
                            <MudListItem T="string">
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                    <div>
                                        <MudText Typo="Typo.body2">@repairOrder.OrderNumber</MudText>
                                        <MudText Typo="Typo.caption">@repairOrder.EquipmentName</MudText>
                                    </div>
                                    <MudChip T="string" Color="@GetUrgencyColor(repairOrder.UrgencyLevel)" Size="Size.Small">
                                        @GetUrgencyName(repairOrder.UrgencyLevel)
                                    </MudChip>
                                </MudStack>
                            </MudListItem>
                        }
                        @if (SelectedRepairOrders.Count > 10)
                        {
                            <MudListItem T="string">
                                <MudText Typo="Typo.caption" Style="font-style: italic;">
                                    还有 @(SelectedRepairOrders.Count - 10) 个报修单...
                                </MudText>
                            </MudListItem>
                        }
                    </MudList>
                </MudCardContent>
            </MudCard>

            <!-- 分配策略选择 -->
            <MudCard Class="mb-4">
                <MudCardContent>
                    <MudText Typo="Typo.subtitle1" Class="mb-3">分配策略</MudText>
                    <MudRadioGroup T="AssignmentStrategy" @bind-SelectedOption="assignmentStrategy">
                        <MudRadio T="AssignmentStrategy" Option="AssignmentStrategy.SingleTechnician">
                            分配给单个技术员
                        </MudRadio>
                        <MudRadio T="AssignmentStrategy" Option="AssignmentStrategy.AutoBalance">
                            自动平衡分配
                        </MudRadio>
                        <MudRadio T="AssignmentStrategy" Option="AssignmentStrategy.ByPriority">
                            按优先级智能分配
                        </MudRadio>
                    </MudRadioGroup>
                </MudCardContent>
            </MudCard>

            <!-- 技术员选择（单个技术员模式） -->
            @if (assignmentStrategy == AssignmentStrategy.SingleTechnician)
            {
                <MudCard Class="mb-4">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-3">选择技术员</MudText>
                        
                        @if (loading)
                        {
                            <MudProgressLinear Indeterminate="true" />
                        }
                        else if (availableTechnicians.Any())
                        {
                            <MudSelect T="int?" @bind-Value="selectedTechnicianId" Label="技术员" Variant="Variant.Outlined">
                                @foreach (var technician in availableTechnicians)
                                {
                                    <MudSelectItem T="int?" Value="technician.Id">
                                        @technician.DisplayName - 当前任务: @GetTechnicianWorkload(technician.Id)
                                    </MudSelectItem>
                                }
                            </MudSelect>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Warning">
                                当前没有可用的技术员
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>
            }

            <!-- 分配预览（自动分配模式） -->
            @if (assignmentStrategy != AssignmentStrategy.SingleTechnician && assignmentPreview.Any())
            {
                <MudCard Class="mb-4">
                    <MudCardContent>
                        <MudText Typo="Typo.subtitle1" Class="mb-3">分配预览</MudText>
                        <MudList T="string" Dense="true" Style="max-height: 300px; overflow-y: auto;">
                            @foreach (var preview in assignmentPreview)
                            {
                                <MudListItem T="string">
                                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                        <div>
                                            <MudText Typo="Typo.body2">@preview.TechnicianName</MudText>
                                            <MudText Typo="Typo.caption">分配 @preview.AssignedCount 个任务</MudText>
                                        </div>
                                        <MudChip T="string" Color="Color.Info" Size="Size.Small">
                                            总任务: @(preview.CurrentWorkload + preview.AssignedCount)
                                        </MudChip>
                                    </MudStack>
                                </MudListItem>
                            }
                        </MudList>
                    </MudCardContent>
                </MudCard>
            }

            <!-- 分配备注 -->
            <MudTextField @bind-Value="assignmentComment"
                        Label="分配备注"
                        Placeholder="可选：添加批量分配说明..."
                        Lines="3"
                        Variant="Variant.Outlined"
                        Class="mb-4" />
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="ConfirmBatchAssignment"
                 Disabled="@(IsAssignmentDisabled() || assigning)">
            @if (assigning)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">分配中...</MudText>
            }
            else
            {
                <MudText>确认分配</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public List<RepairOrderDetailDto> SelectedRepairOrders { get; set; } = new();
    [Parameter] public int AssignedByUserId { get; set; }

    private bool loading = true;
    private bool assigning = false;
    private AssignmentStrategy assignmentStrategy = AssignmentStrategy.SingleTechnician;
    private int? selectedTechnicianId;
    private string assignmentComment = string.Empty;
    private List<User> availableTechnicians = new();
    private Dictionary<int, TechnicianWorkloadDto> technicianWorkloads = new();
    private List<AssignmentPreviewDto> assignmentPreview = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadTechnicians();
        await GenerateAssignmentPreview();
    }

    private async Task LoadTechnicians()
    {
        loading = true;
        try
        {
            if (!SelectedRepairOrders.Any()) return;

            // 获取第一个报修单的维修部门ID（假设都是同一个部门）
            var departmentId = SelectedRepairOrders.First().MaintenanceDepartmentId;
            
            // 获取可用技术员
            availableTechnicians = await RepairOrderService.GetAvailableTechniciansForRepairOrderAsync(SelectedRepairOrders.First().Id);
            
            // 获取技术员工作负载
            var workloadList = await MaintenanceDashboardService.GetTechnicianWorkloadAsync(departmentId);
            technicianWorkloads = workloadList.ToDictionary(w => w.UserId, w => w);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载技术员信息失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task GenerateAssignmentPreview()
    {
        if (assignmentStrategy == AssignmentStrategy.SingleTechnician) return;

        assignmentPreview.Clear();

        if (assignmentStrategy == AssignmentStrategy.AutoBalance)
        {
            // 平衡分配：按当前工作负载平均分配
            var availableTechs = availableTechnicians.ToList();
            if (!availableTechs.Any()) return;

            var totalTasks = SelectedRepairOrders.Count;
            var tasksPerTech = totalTasks / availableTechs.Count;
            var extraTasks = totalTasks % availableTechs.Count;

            for (int i = 0; i < availableTechs.Count; i++)
            {
                var tech = availableTechs[i];
                var assignedCount = tasksPerTech + (i < extraTasks ? 1 : 0);
                var currentWorkload = GetTechnicianWorkload(tech.Id);

                assignmentPreview.Add(new AssignmentPreviewDto
                {
                    TechnicianId = tech.Id,
                    TechnicianName = tech.DisplayName,
                    AssignedCount = assignedCount,
                    CurrentWorkload = currentWorkload
                });
            }
        }
        else if (assignmentStrategy == AssignmentStrategy.ByPriority)
        {
            // 按优先级分配：紧急任务给高级技术员
            var urgentOrders = SelectedRepairOrders.Where(ro => ro.UrgencyLevel <= 2).ToList();
            var normalOrders = SelectedRepairOrders.Where(ro => ro.UrgencyLevel > 2).ToList();

            var seniorTechs = availableTechnicians.ToList(); // 简化：所有技术员都视为高级
            var juniorTechs = availableTechnicians.ToList();

            // 分配紧急任务给技术员
            foreach (var tech in seniorTechs)
            {
                var urgentCount = urgentOrders.Count / Math.Max(seniorTechs.Count, 1);
                var currentWorkload = GetTechnicianWorkload(tech.Id);

                assignmentPreview.Add(new AssignmentPreviewDto
                {
                    TechnicianId = tech.Id,
                    TechnicianName = tech.DisplayName,
                    AssignedCount = urgentCount,
                    CurrentWorkload = currentWorkload
                });
            }

            // 分配普通任务给技术员
            foreach (var tech in juniorTechs)
            {
                var normalCount = normalOrders.Count / Math.Max(juniorTechs.Count, 1);
                var currentWorkload = GetTechnicianWorkload(tech.Id);

                var existingPreview = assignmentPreview.FirstOrDefault(p => p.TechnicianId == tech.Id);
                if (existingPreview != null)
                {
                    existingPreview.AssignedCount += normalCount;
                }
                else
                {
                    assignmentPreview.Add(new AssignmentPreviewDto
                    {
                        TechnicianId = tech.Id,
                        TechnicianName = tech.DisplayName,
                        AssignedCount = normalCount,
                        CurrentWorkload = currentWorkload
                    });
                }
            }
        }

        StateHasChanged();
    }

    private async Task ConfirmBatchAssignment()
    {
        assigning = true;
        try
        {
            if (assignmentStrategy == AssignmentStrategy.SingleTechnician)
            {
                if (selectedTechnicianId == null)
                {
                    Snackbar.Add("请选择技术员", Severity.Warning);
                    return;
                }

                var repairOrderIds = SelectedRepairOrders.Select(ro => ro.Id).ToList();
                var result = await MaintenanceDashboardService.BatchAssignRepairOrdersAsync(
                    repairOrderIds, 
                    selectedTechnicianId.Value, 
                    AssignedByUserId);

                if (result.IsSuccess)
                {
                    Snackbar.Add(result.ErrorMessage, Severity.Success);
                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    Snackbar.Add($"批量分配失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            else
            {
                // TODO: 实现自动分配逻辑
                Snackbar.Add("自动分配功能待实现", Severity.Info);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"批量分配失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            assigning = false;
        }
    }

    private void Cancel() => MudDialog.Cancel();

    private bool IsAssignmentDisabled()
    {
        if (assignmentStrategy == AssignmentStrategy.SingleTechnician)
        {
            return selectedTechnicianId == null;
        }
        return !assignmentPreview.Any();
    }

    private int GetTechnicianWorkload(int technicianId)
    {
        return technicianWorkloads.ContainsKey(technicianId) 
            ? technicianWorkloads[technicianId].AssignedCount 
            : 0;
    }

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,
            2 => Color.Warning,
            3 => Color.Info,
            4 => Color.Default,
            _ => Color.Default
        };
    }

    private string GetUrgencyName(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };
    }

    public enum AssignmentStrategy
    {
        SingleTechnician,
        AutoBalance,
        ByPriority
    }

    public class AssignmentPreviewDto
    {
        public int TechnicianId { get; set; }
        public string TechnicianName { get; set; } = string.Empty;
        public int AssignedCount { get; set; }
        public int CurrentWorkload { get; set; }
    }
}
