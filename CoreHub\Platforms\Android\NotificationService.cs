using Android;
using Android.App;
using Android.Content;
using Android.OS;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using MauiApp5.Shared.Services;

namespace MauiApp5.Platforms.Android
{
    public class NotificationService : INotificationService
    {
        private const string CHANNEL_ID = "MauiApp5_Notifications";
        private const string CHANNEL_NAME = "MauiApp5 通知";
        private const string CHANNEL_DESCRIPTION = "MauiApp5 应用通知";
        private const int NOTIFICATION_ID = 1000;

        public NotificationService()
        {
            CreateNotificationChannel();
        }

        public async Task<bool> RequestPermissionAsync()
        {
            if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu)
            {
                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                var permission = ContextCompat.CheckSelfPermission(context, Manifest.Permission.PostNotifications);
                
                if (permission != global::Android.Content.PM.Permission.Granted)
                {
                    if (Platform.CurrentActivity is AndroidX.AppCompat.App.AppCompatActivity activity)
                    {
                        ActivityCompat.RequestPermissions(activity, new[] { Manifest.Permission.PostNotifications }, 1);
                        // 简化处理，实际应用中应该等待权限结果
                        await Task.Delay(1000);
                        permission = ContextCompat.CheckSelfPermission(context, Manifest.Permission.PostNotifications);
                        return permission == global::Android.Content.PM.Permission.Granted;
                    }
                    return false;
                }
                return true;
            }
            return true; // Android 13以下版本不需要运行时权限
        }

        public async Task SendNotificationAsync(string title, string message)
        {
            var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
            
            // 检查权限
            if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu)
            {
                var hasPermission = await RequestPermissionAsync();
                if (!hasPermission)
                {
                    return;
                }
            }

            var intent = new Intent(context, typeof(MainActivity));
            intent.SetFlags(ActivityFlags.ClearTop | ActivityFlags.SingleTop);
            
            var pendingIntent = PendingIntent.GetActivity(
                context, 
                0, 
                intent, 
                PendingIntentFlags.UpdateCurrent | PendingIntentFlags.Immutable);

            var notificationBuilder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .SetSmallIcon(global::Android.Resource.Drawable.IcDialogInfo)
                .SetContentTitle(title)
                .SetContentText(message)
                .SetPriority(NotificationCompat.PriorityDefault)
                .SetContentIntent(pendingIntent)
                .SetAutoCancel(true)
                .SetVibrate(new long[] { 0, 250, 250, 250 });

            var notificationManager = NotificationManagerCompat.From(context);
            
            try
            {
                notificationManager.Notify(NOTIFICATION_ID, notificationBuilder.Build());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"发送通知失败: {ex.Message}");
            }
        }

        private void CreateNotificationChannel()
        {
            if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
            {
                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                var channel = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationImportance.Default)
                {
                    Description = CHANNEL_DESCRIPTION
                };
                channel.EnableVibration(true);
                channel.SetVibrationPattern(new long[] { 0, 250, 250, 250 });

                var notificationManager = context.GetSystemService(Context.NotificationService) as NotificationManager;
                notificationManager?.CreateNotificationChannel(channel);
            }
        }
    }
} 