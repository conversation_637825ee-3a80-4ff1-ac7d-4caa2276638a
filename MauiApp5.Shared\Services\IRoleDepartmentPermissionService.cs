using MauiApp5.Shared.Models.Database;
using MauiApp5.Shared.Data;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 角色部门权限服务接口
    /// </summary>
    public interface IRoleDepartmentPermissionService
    {
        #region 权限查询

        /// <summary>
        /// 获取用户可以报修设备的部门列表
        /// </summary>
        Task<List<Department>> GetUserReportableDepartmentsAsync(int userId);

        /// <summary>
        /// 获取用户可以接收报修的部门列表
        /// </summary>
        Task<List<Department>> GetUserReceivableDepartmentsAsync(int userId);

        /// <summary>
        /// 获取用户可以维修设备的部门列表
        /// </summary>
        Task<List<Department>> GetUserMaintainableDepartmentsAsync(int userId);

        /// <summary>
        /// 检查用户是否可以报修指定部门的设备
        /// </summary>
        Task<bool> CanUserReportEquipmentAsync(int userId, int departmentId);

        /// <summary>
        /// 检查用户是否可以接收指定部门的报修
        /// </summary>
        Task<bool> CanUserReceiveRepairAsync(int userId, int departmentId);

        /// <summary>
        /// 检查用户是否可以维修指定部门的设备
        /// </summary>
        Task<bool> CanUserMaintainEquipmentAsync(int userId, int departmentId);

        /// <summary>
        /// 获取用户在指定部门的权限类型列表
        /// </summary>
        Task<List<RoleDepartmentPermissionType>> GetUserDepartmentPermissionsAsync(int userId, int departmentId);

        #endregion

        #region 权限管理

        /// <summary>
        /// 获取角色部门权限列表
        /// </summary>
        Task<List<RoleDepartmentPermission>> GetRoleDepartmentPermissionsAsync(int? roleId = null, int? departmentId = null);

        /// <summary>
        /// 创建角色部门权限
        /// </summary>
        Task<bool> CreateRoleDepartmentPermissionAsync(RoleDepartmentPermission permission);

        /// <summary>
        /// 更新角色部门权限
        /// </summary>
        Task<bool> UpdateRoleDepartmentPermissionAsync(RoleDepartmentPermission permission);

        /// <summary>
        /// 删除角色部门权限
        /// </summary>
        Task<bool> DeleteRoleDepartmentPermissionAsync(int permissionId);

        /// <summary>
        /// 批量设置角色的部门权限
        /// </summary>
        Task<bool> SetRoleDepartmentPermissionsAsync(int roleId, List<(int DepartmentId, List<RoleDepartmentPermissionType> PermissionTypes)> permissions);

        #endregion
    }

    /// <summary>
    /// 角色部门权限服务实现
    /// </summary>
    public class RoleDepartmentPermissionService : IRoleDepartmentPermissionService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<RoleDepartmentPermissionService> _logger;

        public RoleDepartmentPermissionService(DatabaseContext dbContext, ILogger<RoleDepartmentPermissionService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 权限查询

        public async Task<List<Department>> GetUserReportableDepartmentsAsync(int userId)
        {
            try
            {
                // 先获取用户有权限的部门ID列表
                var departmentIds = await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanReportEquipment &&
                                       rdp.IsEnabled)
                    .Select((rdp, ur) => rdp.DepartmentId)
                    .ToListAsync();

                if (!departmentIds.Any())
                {
                    return new List<Department>();
                }

                // 再获取对应的部门信息
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => departmentIds.Contains(d.Id) && d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可报修部门列表失败：{userId}", userId);
                return new List<Department>();
            }
        }

        public async Task<List<Department>> GetUserReceivableDepartmentsAsync(int userId)
        {
            try
            {
                // 先获取用户有权限的部门ID列表
                var departmentIds = await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanReceiveRepair &&
                                       rdp.IsEnabled)
                    .Select((rdp, ur) => rdp.DepartmentId)
                    .ToListAsync();

                if (!departmentIds.Any())
                {
                    return new List<Department>();
                }

                // 再获取对应的部门信息
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => departmentIds.Contains(d.Id) && d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可接收报修部门列表失败：{userId}", userId);
                return new List<Department>();
            }
        }

        public async Task<List<Department>> GetUserMaintainableDepartmentsAsync(int userId)
        {
            try
            {
                // 先获取用户有权限的部门ID列表
                var departmentIds = await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanMaintainEquipment &&
                                       rdp.IsEnabled)
                    .Select((rdp, ur) => rdp.DepartmentId)
                    .ToListAsync();

                if (!departmentIds.Any())
                {
                    return new List<Department>();
                }

                // 再获取对应的部门信息
                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => departmentIds.Contains(d.Id) && d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户可维修部门列表失败：{userId}", userId);
                return new List<Department>();
            }
        }

        public async Task<bool> CanUserReportEquipmentAsync(int userId, int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.DepartmentId == departmentId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanReportEquipment &&
                                       rdp.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户报修权限失败：{userId}, {departmentId}", userId, departmentId);
                return false;
            }
        }

        public async Task<bool> CanUserReceiveRepairAsync(int userId, int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.DepartmentId == departmentId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanReceiveRepair &&
                                       rdp.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户接收报修权限失败：{userId}, {departmentId}", userId, departmentId);
                return false;
            }
        }

        public async Task<bool> CanUserMaintainEquipmentAsync(int userId, int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.DepartmentId == departmentId &&
                                       rdp.PermissionType == (int)RoleDepartmentPermissionType.CanMaintainEquipment &&
                                       rdp.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户维修权限失败：{userId}, {departmentId}", userId, departmentId);
                return false;
            }
        }

        public async Task<List<RoleDepartmentPermissionType>> GetUserDepartmentPermissionsAsync(int userId, int departmentId)
        {
            try
            {
                var permissions = await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .InnerJoin<UserRole>((rdp, ur) => rdp.RoleId == ur.RoleId)
                    .Where((rdp, ur) => ur.UserId == userId &&
                                       rdp.DepartmentId == departmentId &&
                                       rdp.IsEnabled)
                    .Select((rdp, ur) => rdp.PermissionType)
                    .ToListAsync();

                return permissions.Cast<RoleDepartmentPermissionType>().ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户部门权限失败：{userId}, {departmentId}", userId, departmentId);
                return new List<RoleDepartmentPermissionType>();
            }
        }

        #endregion

        #region 权限管理

        public async Task<List<RoleDepartmentPermission>> GetRoleDepartmentPermissionsAsync(int? roleId = null, int? departmentId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .LeftJoin<Role>((rdp, r) => rdp.RoleId == r.Id)
                    .LeftJoin<Department>((rdp, r, d) => rdp.DepartmentId == d.Id);

                if (roleId.HasValue)
                {
                    query = query.Where((rdp, r, d) => rdp.RoleId == roleId.Value);
                }

                if (departmentId.HasValue)
                {
                    query = query.Where((rdp, r, d) => rdp.DepartmentId == departmentId.Value);
                }

                return await query.Select((rdp, r, d) => new RoleDepartmentPermission
                    {
                        Id = rdp.Id,
                        RoleId = rdp.RoleId,
                        DepartmentId = rdp.DepartmentId,
                        PermissionType = rdp.PermissionType,
                        IsEnabled = rdp.IsEnabled,
                        CreatedAt = rdp.CreatedAt,
                        CreatedBy = rdp.CreatedBy,
                        UpdatedAt = rdp.UpdatedAt,
                        UpdatedBy = rdp.UpdatedBy,
                        Remark = rdp.Remark,
                        Role = r,
                        Department = d
                    }).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取角色部门权限列表失败：{roleId}, {departmentId}", roleId, departmentId);
                return new List<RoleDepartmentPermission>();
            }
        }

        public async Task<bool> CreateRoleDepartmentPermissionAsync(RoleDepartmentPermission permission)
        {
            try
            {
                // 检查是否已存在相同的权限配置
                var exists = await _dbContext.Db.Queryable<RoleDepartmentPermission>()
                    .Where(rdp => rdp.RoleId == permission.RoleId &&
                                 rdp.DepartmentId == permission.DepartmentId &&
                                 rdp.PermissionType == permission.PermissionType)
                    .AnyAsync();

                if (exists)
                {
                    _logger.LogWarning("角色部门权限已存在：{roleId}, {departmentId}, {permissionType}",
                        permission.RoleId, permission.DepartmentId, permission.PermissionType);
                    return false;
                }

                permission.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(permission).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建角色部门权限失败：{roleId}, {departmentId}, {permissionType}",
                    permission.RoleId, permission.DepartmentId, permission.PermissionType);
                return false;
            }
        }

        public async Task<bool> UpdateRoleDepartmentPermissionAsync(RoleDepartmentPermission permission)
        {
            try
            {
                permission.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(permission).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新角色部门权限失败：{permissionId}", permission.Id);
                return false;
            }
        }

        public async Task<bool> DeleteRoleDepartmentPermissionAsync(int permissionId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<RoleDepartmentPermission>()
                    .Where(rdp => rdp.Id == permissionId)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除角色部门权限失败：{permissionId}", permissionId);
                return false;
            }
        }

        public async Task<bool> SetRoleDepartmentPermissionsAsync(int roleId, List<(int DepartmentId, List<RoleDepartmentPermissionType> PermissionTypes)> permissions)
        {
            try
            {
                // 开始事务
                await _dbContext.Db.Ado.BeginTranAsync();

                try
                {
                    // 删除现有权限
                    await _dbContext.Db.Deleteable<RoleDepartmentPermission>()
                        .Where(rdp => rdp.RoleId == roleId)
                        .ExecuteCommandAsync();

                    // 插入新权限
                    var newPermissions = new List<RoleDepartmentPermission>();
                    foreach (var (departmentId, permissionTypes) in permissions)
                    {
                        foreach (var permissionType in permissionTypes)
                        {
                            newPermissions.Add(new RoleDepartmentPermission
                            {
                                RoleId = roleId,
                                DepartmentId = departmentId,
                                PermissionType = (int)permissionType,
                                IsEnabled = true,
                                CreatedAt = DateTime.Now
                            });
                        }
                    }

                    if (newPermissions.Any())
                    {
                        await _dbContext.Db.Insertable(newPermissions).ExecuteCommandAsync();
                    }

                    await _dbContext.Db.Ado.CommitTranAsync();
                    return true;
                }
                catch
                {
                    await _dbContext.Db.Ado.RollbackTranAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量设置角色部门权限失败：{roleId}", roleId);
                return false;
            }
        }

        #endregion
    }
}
