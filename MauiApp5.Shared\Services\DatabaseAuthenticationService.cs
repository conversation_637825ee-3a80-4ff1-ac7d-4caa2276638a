using System.Security.Claims;
using Microsoft.Extensions.Logging;
using MauiApp5.Shared.Data;
using MauiApp5.Shared.Models.Database;


namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 基于数据库的用户认证服务
    /// </summary>
    public class DatabaseAuthenticationService : IUserAuthenticationService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<DatabaseAuthenticationService> _logger;

        public DatabaseAuthenticationService(DatabaseContext dbContext, ILogger<DatabaseAuthenticationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 验证用户登录
        /// </summary>
        public async Task<UserLoginResult> ValidateUserAsync(string username, string password)
        {
            try
            {
                _logger.LogInformation("开始验证用户：{username}", username);

                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "用户名和密码不能为空"
                    };
                }

                // 查询用户信息
                var user = await _dbContext.Users
                    .Where(u => u.Username == username)
                    .FirstAsync();

                if (user == null)
                {
                    _logger.LogWarning("用户不存在：{username}", username);
                    
                    // 记录登录失败（防止用户枚举攻击，返回统一错误信息）
                    await RecordLoginFailureAsync(username, "用户不存在");
                    
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "用户名或密码错误"
                    };
                }

                // 检查用户状态
                if (!user.IsEnabled)
                {
                    _logger.LogWarning("用户已禁用：{username}", username);
                    await RecordLoginFailureAsync(username, "用户已禁用");
                    
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "账户已被禁用，请联系管理员"
                    };
                }

                if (user.IsLocked)
                {
                    _logger.LogWarning("用户已锁定：{username}", username);
                    await RecordLoginFailureAsync(username, "用户已锁定");
                    
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"账户已被锁定：{user.LockReason}"
                    };
                }

                // 验证密码（明文比较，暂不加密）
                if (password != user.PasswordHash)
                {
                    _logger.LogWarning("密码验证失败：{username}", username);
                    
                    // 增加登录失败次数
                    await IncrementLoginFailureAsync(user);
                    
                    return new UserLoginResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "用户名或密码错误"
                    };
                }

                // 登录成功，重置失败次数并更新登录信息
                await UpdateLoginSuccessAsync(user);

                // 获取用户权限
                var userPermissions = await GetUserPermissionsAsync(user.Id);

                // 构建UserInfo
                var userInfo = new UserInfo
                {
                    UserId = user.Id,
                    Username = user.Username,
                    DisplayName = user.DisplayName,
                    Email = user.Email ?? "",
                    Role = await GetUserRoleAsync(user.Id),
                    Permissions = userPermissions
                };

                _logger.LogInformation("用户验证成功：{username}", username);

                return new UserLoginResult
                {
                    IsSuccess = true,
                    User = userInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户时发生异常：{username}", username);
                return new UserLoginResult
                {
                    IsSuccess = false,
                    ErrorMessage = "验证过程中发生错误，请稍后重试"
                };
            }
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        public UserInfo? GetUserInfo(string username)
        {
            try
            {
                var user = _dbContext.Users
                    .Where(u => u.Username == username && u.IsEnabled)
                    .First();

                if (user == null) return null;

                var userPermissions = GetUserPermissionsAsync(user.Id).GetAwaiter().GetResult();
                var userRole = GetUserRoleAsync(user.Id).GetAwaiter().GetResult();

                return new UserInfo
                {
                    UserId = user.Id,
                    Username = user.Username,
                    DisplayName = user.DisplayName,
                    Email = user.Email ?? "",
                    Role = userRole,
                    Permissions = userPermissions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息失败：{username}", username);
                return null;
            }
        }

        /// <summary>
        /// 获取所有用户
        /// </summary>
        public List<UserInfo> GetAllUsers()
        {
            try
            {
                var users = _dbContext.Users
                    .Where(u => u.IsEnabled)
                    .ToList();

                var userInfos = new List<UserInfo>();

                foreach (var user in users)
                {
                    var userPermissions = GetUserPermissionsAsync(user.Id).GetAwaiter().GetResult();
                    var userRole = GetUserRoleAsync(user.Id).GetAwaiter().GetResult();

                    userInfos.Add(new UserInfo
                    {
                        UserId = user.Id,
                        Username = user.Username,
                        DisplayName = user.DisplayName,
                        Email = user.Email ?? "",
                        Role = userRole,
                        Permissions = userPermissions
                    });
                }

                return userInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户失败");
                return new List<UserInfo>();
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        private async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                var permissions = new HashSet<string>();

                // 1. 通过角色获取权限
                var rolePermissions = await _dbContext.Db.Queryable<UserRole>()
                    .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                    .LeftJoin<RolePermission>((ur, r, rp) => r.Id == rp.RoleId)
                    .LeftJoin<Permission>((ur, r, rp, p) => rp.PermissionId == p.Id)
                    .Where((ur, r, rp, p) => ur.UserId == userId 
                        && ur.IsEnabled 
                        && r.IsEnabled 
                        && rp.IsEnabled 
                        && p.IsEnabled
                        && (ur.ExpiresAt == null || ur.ExpiresAt > DateTime.Now))
                    .Select((ur, r, rp, p) => p.Code)
                    .ToListAsync();

                foreach (var permission in rolePermissions.Where(p => !string.IsNullOrEmpty(p)))
                {
                    permissions.Add(permission);
                }

                // 2. 获取用户直接权限（授权）
                var directPermissions = await _dbContext.Db.Queryable<UserPermission>()
                    .LeftJoin<Permission>((up, p) => up.PermissionId == p.Id)
                    .Where((up, p) => up.UserId == userId 
                        && up.IsEnabled 
                        && up.IsGranted 
                        && p.IsEnabled
                        && (up.ExpiresAt == null || up.ExpiresAt > DateTime.Now))
                    .Select((up, p) => p.Code)
                    .ToListAsync();

                foreach (var permission in directPermissions.Where(p => !string.IsNullOrEmpty(p)))
                {
                    permissions.Add(permission);
                }

                // 3. 移除用户直接权限（拒绝）
                var deniedPermissions = await _dbContext.Db.Queryable<UserPermission>()
                    .LeftJoin<Permission>((up, p) => up.PermissionId == p.Id)
                    .Where((up, p) => up.UserId == userId 
                        && up.IsEnabled 
                        && !up.IsGranted 
                        && p.IsEnabled
                        && (up.ExpiresAt == null || up.ExpiresAt > DateTime.Now))
                    .Select((up, p) => p.Code)
                    .ToListAsync();

                foreach (var permission in deniedPermissions.Where(p => !string.IsNullOrEmpty(p)))
                {
                    permissions.Remove(permission);
                }

                return permissions.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户权限失败：{userId}", userId);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取用户主要角色
        /// </summary>
        private async Task<string> GetUserRoleAsync(int userId)
        {
            try
            {
                var role = await _dbContext.Db.Queryable<UserRole>()
                    .LeftJoin<Role>((ur, r) => ur.RoleId == r.Id)
                    .Where((ur, r) => ur.UserId == userId && ur.IsEnabled && r.IsEnabled)
                    .OrderBy((ur, r) => r.SortOrder)
                    .Select((ur, r) => r.Name)
                    .FirstAsync();

                return role ?? "未分配角色";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户角色失败：{userId}", userId);
                return "未知角色";
            }
        }

        /// <summary>
        /// 记录登录失败
        /// </summary>
        private async Task RecordLoginFailureAsync(string username, string reason)
        {
            try
            {
                _logger.LogWarning("登录失败记录：{username}，原因：{reason}", username, reason);
                // 这里可以记录到日志表或审计表
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录登录失败时发生异常");
            }
        }

        /// <summary>
        /// 增加登录失败次数
        /// </summary>
        private async Task IncrementLoginFailureAsync(User user)
        {
            try
            {
                user.LoginFailureCount++;
                user.UpdatedAt = DateTime.Now;

                // 如果失败次数超过限制，锁定账户
                if (user.LoginFailureCount >= 5)
                {
                    user.IsLocked = true;
                    user.LockedAt = DateTime.Now;
                    user.LockReason = $"连续登录失败{user.LoginFailureCount}次，账户已自动锁定";
                    
                    _logger.LogWarning("用户账户已锁定：{username}，失败次数：{count}", user.Username, user.LoginFailureCount);
                }

                await _dbContext.Db.Updateable(user)
                    .UpdateColumns(u => new { u.LoginFailureCount, u.UpdatedAt, u.IsLocked, u.LockedAt, u.LockReason })
                    .ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新登录失败次数时发生异常");
            }
        }

        /// <summary>
        /// 更新登录成功信息
        /// </summary>
        private async Task UpdateLoginSuccessAsync(User user)
        {
            try
            {
                user.LoginFailureCount = 0;
                user.LastLoginTime = DateTime.Now;
                user.UpdatedAt = DateTime.Now;
                // TODO: 可以从HttpContext获取IP地址
                // user.LastLoginIp = GetClientIpAddress();

                await _dbContext.Db.Updateable(user)
                    .UpdateColumns(u => new { u.LoginFailureCount, u.LastLoginTime, u.UpdatedAt, u.LastLoginIp })
                    .ExecuteCommandAsync();

                _logger.LogInformation("用户登录信息已更新：{username}", user.Username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新登录成功信息时发生异常");
            }
        }

        #endregion
    }
} 