using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 维修部门权限表 - 定义哪些维修部门可以维修哪些生产部门的设备
    /// </summary>
    [SugarTable("MaintenanceDepartmentPermissions")]
    public class MaintenanceDepartmentPermission
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 维修部门ID（维修类型的部门）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "维修部门ID不能为空")]
        public int MaintenanceDepartmentId { get; set; }

        /// <summary>
        /// 目标部门ID（可以维修的部门）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "目标部门ID不能为空")]
        public int TargetDepartmentId { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        #region 导航属性

        /// <summary>
        /// 维修部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? MaintenanceDepartment { get; set; }

        /// <summary>
        /// 目标部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? TargetDepartment { get; set; }

        #endregion
    }
}
