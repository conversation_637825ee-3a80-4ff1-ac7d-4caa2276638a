@page "/workflow-history-test"
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Models.Database
@inject IRepairWorkflowService RepairWorkflowService
@inject IRepairOrderService RepairOrderService
@inject ISnackbar Snackbar

<PageTitle>工作流历史测试</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">工作流历史功能测试</MudText>

    <MudGrid>
        <!-- 测试控制面板 -->
        <MudItem xs="12" md="4">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">测试控制</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudSelect T="int?" @bind-Value="selectedRepairOrderId" Label="选择报修单" Variant="Variant.Outlined">
                            @foreach (var order in repairOrders)
                            {
                                <MudSelectItem Value="@order.Id">@order.OrderNumber - @order.EquipmentName</MudSelectItem>
                            }
                        </MudSelect>

                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 OnClick="LoadHistory"
                                 Disabled="@(selectedRepairOrderId == null || loading)"
                                 FullWidth="true">
                            @if (loading)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                <MudText Class="ms-2">加载中...</MudText>
                            }
                            else
                            {
                                <MudText>加载历史记录</MudText>
                            }
                        </MudButton>

                        <MudDivider />

                        <MudText Typo="Typo.subtitle2">添加测试记录</MudText>
                        
                        <MudTextField @bind-Value="testAction" 
                                    Label="操作名称" 
                                    Variant="Variant.Outlined" 
                                    Placeholder="例如：测试操作" />
                        
                        <MudTextField @bind-Value="testComment" 
                                    Label="备注" 
                                    Variant="Variant.Outlined" 
                                    Lines="3"
                                    Placeholder="添加测试备注..." />

                        <MudButton Variant="Variant.Outlined" 
                                 Color="Color.Secondary" 
                                 OnClick="AddTestHistory"
                                 Disabled="@(selectedRepairOrderId == null || string.IsNullOrEmpty(testAction))"
                                 FullWidth="true">
                            添加测试历史记录
                        </MudButton>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 历史记录显示 -->
        <MudItem xs="12" md="8">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">历史记录</MudText>
                        @if (selectedRepairOrderId.HasValue)
                        {
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                报修单ID: @selectedRepairOrderId
                            </MudText>
                        }
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (loading)
                    {
                        <MudProgressLinear Indeterminate="true" />
                        <MudText Typo="Typo.body2" Class="mt-2">正在加载历史记录...</MudText>
                    }
                    else if (historyRecords.Any())
                    {
                        <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                            @foreach (var history in historyRecords)
                            {
                                <MudTimelineItem>
                                    <ItemOpposite>
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                                            @history.FormattedTime
                                        </MudText>
                                    </ItemOpposite>
                                    <ItemDot>
                                        <MudIcon Icon="@history.ActionIcon" Color="@GetHistoryColor(history)" />
                                    </ItemDot>
                                    <ItemContent>
                                        <MudPaper Class="pa-3" Elevation="2">
                                            <div class="d-flex justify-space-between align-center mb-2">
                                                <MudText Typo="Typo.subtitle2">@history.Action</MudText>
                                                @if (history.IsStatusChange)
                                                {
                                                    <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                                        @history.StatusChangeDescription
                                                    </MudChip>
                                                }
                                            </div>
                                            @if (!string.IsNullOrEmpty(history.Comment))
                                            {
                                                <MudText Typo="Typo.body2" Class="mb-2">@history.Comment</MudText>
                                            }
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                操作人: @history.UserName | @history.DetailedTime
                                            </MudText>
                                            @if (!string.IsNullOrEmpty(history.AdditionalData))
                                            {
                                                <MudExpansionPanels Elevation="0" Class="mt-2">
                                                    <MudExpansionPanel Text="附加数据">
                                                        <MudPaper Class="pa-2" Style="background-color: #f5f5f5; font-family: monospace; white-space: pre-wrap;">
                                                            @history.AdditionalData
                                                        </MudPaper>
                                                    </MudExpansionPanel>
                                                </MudExpansionPanels>
                                            }
                                        </MudPaper>
                                    </ItemContent>
                                </MudTimelineItem>
                            }
                        </MudTimeline>
                        
                        <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-3">
                            共 @historyRecords.Count 条历史记录
                        </MudText>
                    }
                    else if (selectedRepairOrderId.HasValue)
                    {
                        <MudAlert Severity="Severity.Info">
                            该报修单暂无历史记录
                        </MudAlert>
                    }
                    else
                    {
                        <MudAlert Severity="Severity.Normal">
                            请选择一个报修单查看历史记录
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private bool loading = false;
    private int? selectedRepairOrderId;
    private string testAction = string.Empty;
    private string testComment = string.Empty;
    
    private List<RepairOrderDetailDto> repairOrders = new();
    private List<WorkflowHistoryDto> historyRecords = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadRepairOrders();
    }

    private async Task LoadRepairOrders()
    {
        try
        {
            repairOrders = await RepairOrderService.GetRepairOrderDetailsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载报修单失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadHistory()
    {
        if (!selectedRepairOrderId.HasValue) return;

        loading = true;
        try
        {
            historyRecords = await RepairWorkflowService.GetWorkflowHistoryAsync(selectedRepairOrderId.Value);
            Snackbar.Add($"加载了 {historyRecords.Count} 条历史记录", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载历史记录失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task AddTestHistory()
    {
        if (!selectedRepairOrderId.HasValue || string.IsNullOrEmpty(testAction)) return;

        try
        {
            // 使用管理员用户ID (假设为1)
            var success = await RepairWorkflowService.AddWorkflowHistoryAsync(
                selectedRepairOrderId.Value, 
                1, // 管理员用户ID
                testAction, 
                testComment);

            if (success)
            {
                Snackbar.Add("测试历史记录添加成功", Severity.Success);
                testAction = string.Empty;
                testComment = string.Empty;
                await LoadHistory(); // 刷新历史记录
            }
            else
            {
                Snackbar.Add("添加测试历史记录失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"添加测试历史记录失败: {ex.Message}", Severity.Error);
        }
    }

    private Color GetHistoryColor(WorkflowHistoryDto history)
    {
        return history.Action.ToLower() switch
        {
            var a when a.Contains("创建") => Color.Primary,
            var a when a.Contains("分配") => Color.Info,
            var a when a.Contains("开始") || a.Contains("接受") => Color.Success,
            var a when a.Contains("暂停") => Color.Warning,
            var a when a.Contains("恢复") => Color.Success,
            var a when a.Contains("完成") => Color.Success,
            var a when a.Contains("关闭") => Color.Secondary,
            var a when a.Contains("作废") || a.Contains("取消") => Color.Error,
            var a when a.Contains("审批") => Color.Primary,
            var a when a.Contains("支援") || a.Contains("帮助") => Color.Warning,
            var a when a.Contains("测试") => Color.Tertiary,
            _ => Color.Default
        };
    }
}
