using MauiApp5.Shared.Models.Database;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 设备服务接口
    /// </summary>
    public interface IEquipmentService
    {
        /// <summary>
        /// 获取所有设备
        /// </summary>
        Task<List<Equipment>> GetAllEquipmentAsync();

        /// <summary>
        /// 根据ID获取设备
        /// </summary>
        Task<Equipment?> GetEquipmentByIdAsync(int id);

        /// <summary>
        /// 根据编码获取设备
        /// </summary>
        Task<Equipment?> GetEquipmentByCodeAsync(string code);

        /// <summary>
        /// 创建设备
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateEquipmentAsync(Equipment equipment);

        /// <summary>
        /// 更新设备
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentAsync(Equipment equipment);

        /// <summary>
        /// 删除设备
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentAsync(int id);

        /// <summary>
        /// 切换设备状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 更新设备状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentStatusAsync(int id, int status);

        /// <summary>
        /// 获取启用的设备列表
        /// </summary>
        Task<List<Equipment>> GetEnabledEquipmentAsync();

        /// <summary>
        /// 根据部门获取设备
        /// </summary>
        Task<List<Equipment>> GetEquipmentByDepartmentAsync(int departmentId);

        /// <summary>
        /// 根据位置获取设备
        /// </summary>
        Task<List<Equipment>> GetEquipmentByLocationAsync(int locationId);

        /// <summary>
        /// 根据型号获取设备
        /// </summary>
        Task<List<Equipment>> GetEquipmentByModelAsync(int modelId);

        /// <summary>
        /// 获取设备详细信息（包含关联数据）
        /// </summary>
        Task<List<EquipmentDetailDto>> GetEquipmentDetailsAsync();

        /// <summary>
        /// 搜索设备
        /// </summary>
        Task<List<EquipmentDetailDto>> SearchEquipmentAsync(EquipmentSearchDto searchDto);

        /// <summary>
        /// 检查设备编码是否存在
        /// </summary>
        Task<bool> IsCodeExistsAsync(string code, int? excludeId = null);

        /// <summary>
        /// 获取设备统计信息
        /// </summary>
        Task<EquipmentStatisticsDto> GetEquipmentStatisticsAsync();
    }

    /// <summary>
    /// 设备详细信息DTO
    /// </summary>
    public class EquipmentDetailDto
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public int ModelId { get; set; }
        public string ModelName { get; set; } = string.Empty;
        public string ModelCategory { get; set; } = string.Empty;
        public string? ModelBrand { get; set; }
        public int LocationId { get; set; }
        public string LocationName { get; set; } = string.Empty;
        public string? SerialNumber { get; set; }
        public string? AssetNumber { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public DateTime? WarrantyExpiry { get; set; }
        public int Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public string? Description { get; set; }
        public bool IsEnabled { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Remark { get; set; }
        public int RepairOrderCount { get; set; }
    }

    /// <summary>
    /// 设备搜索DTO
    /// </summary>
    public class EquipmentSearchDto
    {
        public string? SearchText { get; set; }
        public int? DepartmentId { get; set; }
        public int? ModelId { get; set; }
        public int? LocationId { get; set; }
        public int? Status { get; set; }
        public bool? IsEnabled { get; set; }
        public DateTime? PurchaseDateFrom { get; set; }
        public DateTime? PurchaseDateTo { get; set; }
        public DateTime? WarrantyExpiryFrom { get; set; }
        public DateTime? WarrantyExpiryTo { get; set; }
    }

    /// <summary>
    /// 设备统计信息DTO
    /// </summary>
    public class EquipmentStatisticsDto
    {
        public int TotalCount { get; set; }
        public int NormalCount { get; set; }
        public int MaintenanceCount { get; set; }
        public int DisabledCount { get; set; }
        public int ScrapCount { get; set; }
        public int WarrantyExpiredCount { get; set; }
        public int MaintenanceDueCount { get; set; }
        public Dictionary<string, int> CategoryCounts { get; set; } = new();
        public Dictionary<string, int> DepartmentCounts { get; set; } = new();
    }
}
