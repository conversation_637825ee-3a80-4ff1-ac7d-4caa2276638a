using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 用户实体
    /// </summary>
    [SugarTable("Users")]
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户名（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码哈希
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        [Required(ErrorMessage = "密码不能为空")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "显示名称不能为空")]
        [StringLength(100, ErrorMessage = "显示名称长度不能超过100个字符")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [EmailAddress(ErrorMessage = "邮箱格式不正确")]
        [StringLength(100, ErrorMessage = "邮箱长度不能超过100个字符")]
        public string? Email { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = true)]
        [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
        public string? Phone { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否锁定
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsLocked { get; set; } = false;

        /// <summary>
        /// 锁定时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LockedAt { get; set; }

        /// <summary>
        /// 锁定原因
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "锁定原因长度不能超过500个字符")]
        public string? LockReason { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最后登录IP
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "IP地址长度不能超过50个字符")]
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 登录失败次数
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int LoginFailureCount { get; set; } = 0;

        /// <summary>
        /// 所属部门ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? DepartmentId { get; set; }

        // 工种关系已改为多对多，通过 UserJobTypes 表管理

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 所属部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? Department { get; set; }

        // 工种关系已改为多对多，通过 UserJobTypes 表管理

        /// <summary>
        /// 用户工种关联（多对多关系）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        [JsonIgnore]
        public List<UserJobType> UserJobTypes { get; set; } = new List<UserJobType>();

        /// <summary>
        /// 用户角色关联
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<UserRole> UserRoles { get; set; } = new List<UserRole>();

        /// <summary>
        /// 用户直接权限
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }
} 