using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 权限实体
    /// </summary>
    [SugarTable("Permissions")]
    public class Permission
    {
        /// <summary>
        /// 权限ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 权限编码（唯一）
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "权限编码不能为空")]
        [StringLength(100, ErrorMessage = "权限编码长度不能超过100个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 权限名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "权限名称不能为空")]
        [StringLength(100, ErrorMessage = "权限名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "权限描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 权限模块
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "权限模块不能为空")]
        [StringLength(50, ErrorMessage = "权限模块长度不能超过50个字符")]
        public string Module { get; set; } = string.Empty;

        /// <summary>
        /// 权限操作
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "权限操作不能为空")]
        [StringLength(50, ErrorMessage = "权限操作长度不能超过50个字符")]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// 父级权限ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 权限级别（1=菜单级,2=页面级,3=功能级,4=数据级）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 2;

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统内置权限
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 路由地址
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "路由地址长度不能超过200个字符")]
        public string? RouteUrl { get; set; }

        /// <summary>
        /// 图标
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "图标长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 角色权限关联
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();

        /// <summary>
        /// 用户直接权限关联
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();

        /// <summary>
        /// 子权限
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Permission> Children { get; set; } = new List<Permission>();

        /// <summary>
        /// 父权限
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Permission? Parent { get; set; }
    }
} 