using MauiApp5.Shared.Data;
using MauiApp5.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 设备服务实现
    /// </summary>
    public class EquipmentService : IEquipmentService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<EquipmentService> _logger;

        public EquipmentService(DatabaseContext dbContext, ILogger<EquipmentService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<List<Equipment>> GetAllEquipmentAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有设备失败");
                throw;
            }
        }

        public async Task<Equipment?> GetEquipmentByIdAsync(int id)
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取设备失败: {id}", id);
                throw;
            }
        }

        public async Task<Equipment?> GetEquipmentByCodeAsync(string code)
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Code == code)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据编码获取设备失败: {code}", code);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CreateEquipmentAsync(Equipment equipment)
        {
            try
            {
                // 检查编码是否存在
                if (await IsCodeExistsAsync(equipment.Code))
                {
                    return (false, "设备编码已存在");
                }

                // 验证关联数据
                var validationResult = await ValidateEquipmentRelationsAsync(equipment);
                if (!validationResult.IsSuccess)
                {
                    return validationResult;
                }

                equipment.CreatedAt = DateTime.Now;
                var result = await _dbContext.Db.Insertable(equipment).ExecuteReturnIdentityAsync();
                
                _logger.LogInformation("创建设备成功: {name} ({code})", equipment.Name, equipment.Code);
                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建设备失败: {name}", equipment.Name);
                return (false, $"创建设备失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentAsync(Equipment equipment)
        {
            try
            {
                // 检查编码是否存在（排除自己）
                if (await IsCodeExistsAsync(equipment.Code, equipment.Id))
                {
                    return (false, "设备编码已存在");
                }

                // 验证关联数据
                var validationResult = await ValidateEquipmentRelationsAsync(equipment);
                if (!validationResult.IsSuccess)
                {
                    return validationResult;
                }

                equipment.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(equipment).ExecuteCommandAsync();
                
                if (result > 0)
                {
                    _logger.LogInformation("更新设备成功: {name} ({code})", equipment.Name, equipment.Code);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "设备不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备失败: {id}", equipment.Id);
                return (false, $"更新设备失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeleteEquipmentAsync(int id)
        {
            try
            {
                // 检查是否有关联的报修单
                var hasRepairOrders = await _dbContext.Db.Queryable<RepairOrder>()
                    .Where(ro => ro.EquipmentId == id)
                    .AnyAsync();

                if (hasRepairOrders)
                {
                    return (false, "该设备还有关联的报修单，无法删除");
                }

                var result = await _dbContext.Db.Deleteable<Equipment>()
                    .Where(e => e.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除设备成功: {id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "设备不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备失败: {id}", id);
                return (false, $"删除设备失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id)
        {
            try
            {
                var equipment = await GetEquipmentByIdAsync(id);
                if (equipment == null)
                {
                    return (false, "设备不存在");
                }

                equipment.IsEnabled = !equipment.IsEnabled;
                equipment.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(equipment)
                    .UpdateColumns(e => new { e.IsEnabled, e.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("切换设备状态成功: {id} -> {status}", id, equipment.IsEnabled);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换设备状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateEquipmentStatusAsync(int id, int status)
        {
            try
            {
                var equipment = await GetEquipmentByIdAsync(id);
                if (equipment == null)
                {
                    return (false, "设备不存在");
                }

                equipment.Status = status;
                equipment.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(equipment)
                    .UpdateColumns(e => new { e.Status, e.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新设备状态成功: {id} -> {status}", id, status);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备状态失败: {id}", id);
                return (false, $"操作失败: {ex.Message}");
            }
        }

        public async Task<List<Equipment>> GetEnabledEquipmentAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的设备列表失败");
                throw;
            }
        }

        public async Task<List<Equipment>> GetEquipmentByDepartmentAsync(int departmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.DepartmentId == departmentId && e.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据部门获取设备失败: {departmentId}", departmentId);
                throw;
            }
        }

        public async Task<List<Equipment>> GetEquipmentByLocationAsync(int locationId)
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.LocationId == locationId && e.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据位置获取设备失败: {locationId}", locationId);
                throw;
            }
        }

        public async Task<List<Equipment>> GetEquipmentByModelAsync(int modelId)
        {
            try
            {
                return await _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.ModelId == modelId && e.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据型号获取设备失败: {modelId}", modelId);
                throw;
            }
        }

        public async Task<List<EquipmentDetailDto>> GetEquipmentDetailsAsync()
        {
            try
            {
                // 简化查询，避免复杂的联表操作
                var equipment = await _dbContext.Db.Queryable<Equipment>().ToListAsync();
                var departments = await _dbContext.Db.Queryable<Department>().ToListAsync();
                var models = await _dbContext.Db.Queryable<EquipmentModel>().ToListAsync();
                var locations = await _dbContext.Db.Queryable<Location>().ToListAsync();
                var repairOrderCounts = await _dbContext.Db.Queryable<RepairOrder>()
                    .GroupBy(ro => ro.EquipmentId)
                    .Select(g => new { EquipmentId = g.EquipmentId, Count = SqlFunc.AggregateCount(g.Id) })
                    .ToListAsync();

                var result = new List<EquipmentDetailDto>();
                foreach (var eq in equipment)
                {
                    var department = departments.FirstOrDefault(d => d.Id == eq.DepartmentId);
                    var model = models.FirstOrDefault(m => m.Id == eq.ModelId);
                    var location = locations.FirstOrDefault(l => l.Id == eq.LocationId);
                    var repairOrderCount = repairOrderCounts.FirstOrDefault(roc => roc.EquipmentId == eq.Id)?.Count ?? 0;

                    result.Add(new EquipmentDetailDto
                    {
                        Id = eq.Id,
                        Code = eq.Code,
                        Name = eq.Name,
                        DepartmentId = eq.DepartmentId,
                        DepartmentName = department?.Name ?? "",
                        ModelId = eq.ModelId,
                        ModelName = model?.Name ?? "",
                        ModelCategory = model?.Category ?? "",
                        ModelBrand = model?.Brand,
                        LocationId = eq.LocationId,
                        LocationName = location?.Name ?? "",
                        SerialNumber = eq.SerialNumber,
                        AssetNumber = eq.AssetNumber,
                        PurchaseDate = eq.PurchaseDate,
                        WarrantyExpiry = eq.WarrantyExpiry,
                        Status = eq.Status,
                        StatusName = eq.StatusName,
                        LastMaintenanceDate = eq.LastMaintenanceDate,
                        NextMaintenanceDate = eq.NextMaintenanceDate,
                        Description = eq.Description,
                        IsEnabled = eq.IsEnabled,
                        CreatedAt = eq.CreatedAt,
                        Remark = eq.Remark,
                        RepairOrderCount = repairOrderCount
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备详细信息失败");
                throw;
            }
        }

        public async Task<List<EquipmentDetailDto>> SearchEquipmentAsync(EquipmentSearchDto searchDto)
        {
            try
            {
                var allEquipment = await GetEquipmentDetailsAsync();
                var filtered = allEquipment.AsEnumerable();

                // 按搜索文本过滤
                if (!string.IsNullOrWhiteSpace(searchDto.SearchText))
                {
                    filtered = filtered.Where(e =>
                        e.Name.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        e.Code.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        e.DepartmentName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        e.ModelName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        e.LocationName.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (e.SerialNumber?.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (e.AssetNumber?.Contains(searchDto.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                    );
                }

                // 按部门过滤
                if (searchDto.DepartmentId.HasValue)
                {
                    filtered = filtered.Where(e => e.DepartmentId == searchDto.DepartmentId.Value);
                }

                // 按型号过滤
                if (searchDto.ModelId.HasValue)
                {
                    filtered = filtered.Where(e => e.ModelId == searchDto.ModelId.Value);
                }

                // 按位置过滤
                if (searchDto.LocationId.HasValue)
                {
                    filtered = filtered.Where(e => e.LocationId == searchDto.LocationId.Value);
                }

                // 按状态过滤
                if (searchDto.Status.HasValue)
                {
                    filtered = filtered.Where(e => e.Status == searchDto.Status.Value);
                }

                // 按启用状态过滤
                if (searchDto.IsEnabled.HasValue)
                {
                    filtered = filtered.Where(e => e.IsEnabled == searchDto.IsEnabled.Value);
                }

                // 按购买日期过滤
                if (searchDto.PurchaseDateFrom.HasValue)
                {
                    filtered = filtered.Where(e => e.PurchaseDate >= searchDto.PurchaseDateFrom.Value);
                }
                if (searchDto.PurchaseDateTo.HasValue)
                {
                    filtered = filtered.Where(e => e.PurchaseDate <= searchDto.PurchaseDateTo.Value);
                }

                // 按保修到期日期过滤
                if (searchDto.WarrantyExpiryFrom.HasValue)
                {
                    filtered = filtered.Where(e => e.WarrantyExpiry >= searchDto.WarrantyExpiryFrom.Value);
                }
                if (searchDto.WarrantyExpiryTo.HasValue)
                {
                    filtered = filtered.Where(e => e.WarrantyExpiry <= searchDto.WarrantyExpiryTo.Value);
                }

                return filtered.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索设备失败");
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string code, int? excludeId = null)
        {
            try
            {
                var query = _dbContext.Db.Queryable<Equipment>()
                    .Where(e => e.Code == code);

                if (excludeId.HasValue)
                {
                    query = query.Where(e => e.Id != excludeId.Value);
                }

                return await query.AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查设备编码是否存在失败: {code}", code);
                throw;
            }
        }

        public async Task<EquipmentStatisticsDto> GetEquipmentStatisticsAsync()
        {
            try
            {
                var allEquipment = await GetEquipmentDetailsAsync();
                var today = DateTime.Today;

                var statistics = new EquipmentStatisticsDto
                {
                    TotalCount = allEquipment.Count,
                    NormalCount = allEquipment.Count(e => e.Status == 1),
                    MaintenanceCount = allEquipment.Count(e => e.Status == 2),
                    DisabledCount = allEquipment.Count(e => e.Status == 3),
                    ScrapCount = allEquipment.Count(e => e.Status == 4),
                    WarrantyExpiredCount = allEquipment.Count(e => e.WarrantyExpiry.HasValue && e.WarrantyExpiry.Value < today),
                    MaintenanceDueCount = allEquipment.Count(e => e.NextMaintenanceDate.HasValue && e.NextMaintenanceDate.Value <= today.AddDays(7))
                };

                // 按类别统计
                statistics.CategoryCounts = allEquipment
                    .GroupBy(e => e.ModelCategory)
                    .ToDictionary(g => g.Key, g => g.Count());

                // 按部门统计
                statistics.DepartmentCounts = allEquipment
                    .GroupBy(e => e.DepartmentName)
                    .ToDictionary(g => g.Key, g => g.Count());

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取设备统计信息失败");
                throw;
            }
        }

        private async Task<(bool IsSuccess, string ErrorMessage)> ValidateEquipmentRelationsAsync(Equipment equipment)
        {
            try
            {
                // 检查部门是否存在
                var department = await _dbContext.Db.Queryable<Department>()
                    .Where(d => d.Id == equipment.DepartmentId && d.IsEnabled)
                    .FirstAsync();
                if (department == null)
                {
                    return (false, "所属部门不存在或已禁用");
                }

                // 检查设备型号是否存在
                var model = await _dbContext.Db.Queryable<EquipmentModel>()
                    .Where(m => m.Id == equipment.ModelId && m.IsEnabled)
                    .FirstAsync();
                if (model == null)
                {
                    return (false, "设备型号不存在或已禁用");
                }

                // 检查位置是否存在
                var location = await _dbContext.Db.Queryable<Location>()
                    .Where(l => l.Id == equipment.LocationId && l.IsEnabled)
                    .FirstAsync();
                if (location == null)
                {
                    return (false, "所在位置不存在或已禁用");
                }

                // 检查位置是否属于指定部门
                if (location.DepartmentId != equipment.DepartmentId)
                {
                    return (false, "所选位置不属于指定部门");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证设备关联数据失败");
                return (false, $"验证失败: {ex.Message}");
            }
        }
    }
}
