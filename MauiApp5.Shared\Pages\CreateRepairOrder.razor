@page "/create-repair-order"
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@using FluentValidation
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Logging
@using System.Security.Claims
@inject IRepairOrderService RepairOrderService
@inject IEquipmentService EquipmentService
@inject IDepartmentService DepartmentService
@inject IUserManagementService UserManagementService
@inject IRoleDepartmentAssignmentServiceV2 RoleDepartmentAssignmentService
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService

@inject IDepartmentTypeService DepartmentTypeService
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject ILogger<CreateRepairOrder> Logger

<PageTitle>设备报修</PageTitle>

<AuthorizeView>
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h4" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                            设备报修
                        </MudText>
                    </MudItem>

                    <MudItem xs="12">
                        <MudForm @ref="form" Model="@repairOrder" Validation="@(new RepairOrderValidator())">
                            <MudGrid>
                                <!-- 设备选择 -->
                                <MudItem xs="12">
                                    <MudCard>
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <MudText Typo="Typo.h6">
                                                    <MudIcon Icon="@Icons.Material.Filled.Devices" Class="mr-2" />
                                                    选择设备
                                                </MudText>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent>
                                            <MudGrid>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int?" Value="selectedDepartmentId"
                                                        Label="设备所属部门" ValueChanged="OnDepartmentChanged"
                                                        Clearable="true" Disabled="@(!hasReportPermission)">
                                                        @foreach (var dept in reportableDepartments)
                                                        {
                                                            <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (!hasReportPermission)
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Error" Class="mt-1">
                                                            您没有设备报修权限
                                                        </MudText>
                                                    }
                                                    else if (!reportableDepartments.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            没有可报修的部门
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudTextField @bind-Value="equipmentSearchText" Label="搜索设备"
                                                        Placeholder="输入设备名称或编码..." Adornment="Adornment.Start"
                                                        AdornmentIcon="@Icons.Material.Filled.Search"
                                                        OnKeyUp="OnEquipmentSearchKeyUp" Immediate="true" />
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudSelect T="int" Value="repairOrder.EquipmentId"
                                                        For="@(() => repairOrder.EquipmentId)" Label="选择设备"
                                                        Required="true" ValueChanged="OnEquipmentChanged">
                                                        @foreach (var equipment in filteredEquipment)
                                                        {
                                                            <MudSelectItem T="int" Value="@equipment.Id">
                                                                <div class="d-flex align-center">
                                                                    <div class="flex-grow-1">
                                                                        <MudText Typo="Typo.body1">@equipment.Name</MudText>
                                                                        <MudText Typo="Typo.caption"
                                                                            Color="Color.Secondary">
                                                                            @equipment.Code | @equipment.DepartmentName |
                                                                            @equipment.LocationName
                                                                        </MudText>
                                                                    </div>
                                                                    <MudChip T="string"
                                                                        Color="@GetEquipmentStatusColor(equipment.Status)"
                                                                        Size="Size.Small">
                                                                        @equipment.StatusName
                                                                    </MudChip>
                                                                </div>
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                </MudItem>
                                                @if (selectedEquipment != null)
                                                {
                                                    <MudItem xs="12">
                                                        <MudAlert Severity="Severity.Info" Class="mt-2">
                                                            <MudText><strong>设备信息：</strong></MudText>
                                                            <MudText>名称：@selectedEquipment.Name</MudText>
                                                            <MudText>编码：@selectedEquipment.Code</MudText>
                                                            <MudText>位置：@selectedEquipment.DepartmentName -
                                                                @selectedEquipment.LocationName</MudText>
                                                            <MudText>状态：@selectedEquipment.StatusName</MudText>
                                                        </MudAlert>
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>

                                <!-- 故障信息 -->
                                <MudItem xs="12">
                                    <MudCard>
                                        <MudCardHeader>
                                            <CardHeaderContent>
                                                <MudText Typo="Typo.h6">
                                                    <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Class="mr-2" />
                                                    故障信息
                                                </MudText>
                                            </CardHeaderContent>
                                        </MudCardHeader>
                                        <MudCardContent>
                                            <MudGrid>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int" @bind-Value="repairOrder.UrgencyLevel"
                                                        For="@(() => repairOrder.UrgencyLevel)" Label="紧急程度"
                                                        Required="true">
                                                        <MudSelectItem T="int" Value="1">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.PriorityHigh"
                                                                    Color="Color.Error" Class="mr-2" />
                                                                <span>紧急 - 设备完全停机，严重影响生产</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="2">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Warning"
                                                                    Color="Color.Warning" Class="mr-2" />
                                                                <span>高 - 设备功能受限，影响正常使用</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="3">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Info"
                                                                    Color="Color.Info" Class="mr-2" />
                                                                <span>中 - 设备有异常，但不影响基本功能</span>
                                                            </div>
                                                        </MudSelectItem>
                                                        <MudSelectItem T="int" Value="4">
                                                            <div class="d-flex align-center">
                                                                <MudIcon Icon="@Icons.Material.Filled.Schedule"
                                                                    Color="Color.Default" Class="mr-2" />
                                                                <span>低 - 预防性维护或小问题</span>
                                                            </div>
                                                        </MudSelectItem>
                                                    </MudSelect>
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int" Value="repairOrder.MaintenanceDepartmentId"
                                                        For="@(() => repairOrder.MaintenanceDepartmentId)" Label="维修部门"
                                                        Required="true" ValueChanged="OnMaintenanceDepartmentChanged"
                                                        HelperText="@GetMaintenanceDepartmentHelperText()">
                                                        @foreach (var dept in maintenanceDepartments)
                                                        {
                                                            <MudSelectItem T="int" Value="@dept.Id">
                                                                <div class="d-flex align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                                    <span>@dept.Name</span>
                                                                    @if (dept.DepartmentType != null)
                                                                    {
                                                                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                                                            @dept.DepartmentType.Name
                                                                        </MudChip>
                                                                    }
                                                                </div>
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (!maintenanceDepartments.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            @if (selectedEquipment != null)
                                                            {
                                                                <span>没有维修部门可以维修@selectedEquipment.DepartmentName的设备</span>
                                                            }
                                                            else
                                                            {
                                                                <span>没有可用的维修部门</span>
                                                            }
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12" md="6">
                                                    <MudSelect T="int?" @bind-Value="selectedMaintenancePersonnelId"
                                                        Label="指定维修人员" Clearable="true"
                                                        Disabled="@(repairOrder.MaintenanceDepartmentId == 0 || !availablePersonnel.Any())">
                                                        @foreach (var personnel in availablePersonnel)
                                                        {
                                                            <MudSelectItem T="int?" Value="@(personnel.Id)">
                                                                <div class="d-flex align-center">
                                                                    <div class="flex-grow-1">
                                                                        <MudText Typo="Typo.body2">@personnel.DisplayName</MudText>
                                                                        <MudStack Row Spacing="1" Class="mt-1">
                                                                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                                                维修工种
                                                                            </MudChip>
                                                                            <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                                                                可接单
                                                                            </MudChip>
                                                                        </MudStack>
                                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                                            部门: @personnel.Department?.Name
                                                                        </MudText>
                                                                    </div>
                                                                </div>
                                                            </MudSelectItem>
                                                        }
                                                    </MudSelect>
                                                    @if (repairOrder.MaintenanceDepartmentId > 0 && !availablePersonnel.Any())
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Warning" Class="mt-1">
                                                            该部门暂无可用维修人员，系统将自动分配
                                                        </MudText>
                                                    }
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudTextField @bind-Value="repairOrder.FaultDescription"
                                                        For="@(() => repairOrder.FaultDescription)" Label="故障描述"
                                                        Required="true" Lines="5"
                                                        Placeholder="请详细描述设备故障现象、发生时间、可能原因等..."
                                                        HelperText="详细的故障描述有助于维修人员快速定位问题" Immediate="true" />
                                                </MudItem>
                                                <MudItem xs="12">
                                                    <MudTextField @bind-Value="repairOrder.Remark"
                                                        For="@(() => repairOrder.Remark)" Label="补充说明" Lines="2"
                                                        Placeholder="其他需要说明的情况..." Immediate="true" />
                                                </MudItem>
                                            </MudGrid>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>

                                <!-- 操作按钮 -->
                                <MudItem xs="12">
                                    <MudStack Row Justify="Justify.Center" Spacing="4" Class="mt-6">
                                        <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Cancel"
                                            OnClick="Cancel">
                                            取消
                                        </MudButton>
                                        <MudButton Variant="Variant.Filled" Color="Color.Primary"
                                            StartIcon="@Icons.Material.Filled.Send" OnClick="SubmitRepairOrder"
                                            Disabled="@(submitting || !hasReportPermission)">
                                            @if (submitting)
                                            {
                                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                                <MudText Class="ms-2">提交中...</MudText>
                                            }
                                            else
                                            {
                                                <MudText>提交报修</MudText>
                                            }
                                        </MudButton>
                                    </MudStack>
                                </MudItem>
                            </MudGrid>
                        </MudForm>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudContainer>
    </Authorized>
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/login"
                        StartIcon="@Icons.Material.Filled.Login">
                        前往登录
                    </MudButton>
                </MudStack>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</AuthorizeView>

@code {
    private MudForm form = null!;
    private RepairOrder repairOrder = new();
    private List<Department> departments = new();
    private List<Department> reportableDepartments = new(); // 用户可以报修的部门
    private List<Department> maintenanceDepartments = new(); // 用户可以选择的维修部门
    private List<User> availablePersonnel = new(); // 可用的维修人员
    private List<EquipmentDetailDto> equipment = new();
    private List<EquipmentDetailDto> filteredEquipment = new();
    private EquipmentDetailDto? selectedEquipment = null;
    private int? selectedDepartmentId = null;
    private int? selectedMaintenancePersonnelId = null;
    private string equipmentSearchText = string.Empty;
    private bool submitting = false;
    private int currentUserId = 0;
    private bool hasReportPermission = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadBasicData();
        await LoadEquipment();

        // 设置默认值
        repairOrder.UrgencyLevel = 3; // 默认中等紧急程度
        repairOrder.ReporterId = currentUserId;
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    currentUserId = userId;
                }
                else
                {
                    // 如果无法获取用户ID，使用默认值1（这种情况应该很少发生）
                    currentUserId = 1;
                    Snackbar.Add("无法获取用户ID，使用默认值", Severity.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取当前用户信息失败: {ex.Message}", Severity.Error);
            // 使用默认值以防止页面崩溃
            currentUserId = 1;
        }
    }

    private async Task LoadBasicData()
    {
        try
        {
            // 获取所有部门
            departments = await DepartmentService.GetEnabledDepartmentsAsync();

            // 获取用户可以报修设备的部门（基于角色部门分配）
            reportableDepartments = await RoleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(currentUserId);

            // 检查用户是否有报修权限
            hasReportPermission = reportableDepartments.Any();

            if (!hasReportPermission)
            {
                Snackbar.Add("您没有设备报修权限，请联系管理员", Severity.Warning);
            }

            // 初始加载所有维修部门（在选择设备后会根据设备部门进行过滤）
            await LoadAllMaintenanceDepartments();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载基础数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadEquipment()
    {
        try
        {
            equipment = await EquipmentService.GetEquipmentDetailsAsync();
            FilterEquipment();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载设备数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void FilterEquipment()
    {
        var filtered = equipment.AsEnumerable();

        // 首先按用户权限过滤 - 只显示用户有权限报修的部门设备
        var reportableDepartmentIds = reportableDepartments.Select(d => d.Id).ToList();
        if (reportableDepartmentIds.Any())
        {
            filtered = filtered.Where(e => reportableDepartmentIds.Contains(e.DepartmentId));
        }
        else
        {
            // 如果用户没有任何报修权限，则不显示任何设备
            filtered = Enumerable.Empty<EquipmentDetailDto>();
        }

        // 按选择的部门过滤
        if (selectedDepartmentId.HasValue)
        {
            filtered = filtered.Where(e => e.DepartmentId == selectedDepartmentId.Value);
        }

        // 按搜索文本过滤
        if (!string.IsNullOrWhiteSpace(equipmentSearchText))
        {
            filtered = filtered.Where(e =>
            e.Name.Contains(equipmentSearchText, StringComparison.OrdinalIgnoreCase) ||
            e.Code.Contains(equipmentSearchText, StringComparison.OrdinalIgnoreCase) ||
            e.LocationName.Contains(equipmentSearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // 只显示启用的设备
        filtered = filtered.Where(e => e.IsEnabled);

        filteredEquipment = filtered.OrderBy(e => e.DepartmentName).ThenBy(e => e.Name).ToList();
        StateHasChanged();
    }

    private void OnDepartmentChanged(int? departmentId)
    {
        selectedDepartmentId = departmentId;
        repairOrder.EquipmentId = 0; // 重置设备选择
        selectedEquipment = null;
        FilterEquipment();
    }

    private void OnEquipmentSearchKeyUp(KeyboardEventArgs e)
    {
        FilterEquipment();
    }

    private async Task OnEquipmentChanged(int equipmentId)
    {
        repairOrder.EquipmentId = equipmentId;
        selectedEquipment = filteredEquipment.FirstOrDefault(e => e.Id == equipmentId);

        // 当设备改变时，根据设备所属部门过滤可用的维修部门
        if (selectedEquipment != null)
        {
            await FilterMaintenanceDepartmentsByEquipment();

            // 重置维修部门选择
            repairOrder.MaintenanceDepartmentId = 0;
            selectedMaintenancePersonnelId = null;
            availablePersonnel.Clear();
        }
        else
        {
            // 如果没有选择设备，显示所有维修部门
            await LoadAllMaintenanceDepartments();
        }
    }

    private async Task OnMaintenanceDepartmentChanged(int departmentId)
    {
        repairOrder.MaintenanceDepartmentId = departmentId;
        selectedMaintenancePersonnelId = null;
        await LoadMaintenancePersonnel();
    }

    private async Task FilterMaintenanceDepartmentsByEquipment()
    {
        try
        {
            if (selectedEquipment != null)
            {
                // 根据设备所属部门获取可以维修该部门设备的维修部门
                var availableMaintenanceDepts = await MaintenanceDepartmentPermissionService.GetAvailableMaintenanceDepartmentsAsync(selectedEquipment.DepartmentId);

                // 如果没有配置权限关系，则显示所有维修部门
                if (!availableMaintenanceDepts.Any())
                {
                    availableMaintenanceDepts = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();
                    Snackbar.Add($"未配置维修权限关系，显示所有维修部门。建议在维修部门管理中配置权限关系。", Severity.Warning);
                }
                else
                {
                    Snackbar.Add($"已根据设备所属部门筛选出{availableMaintenanceDepts.Count}个可用维修部门", Severity.Info);
                }

                // 加载部门类型信息
                foreach (var dept in availableMaintenanceDepts)
                {
                    if (dept.DepartmentTypeId.HasValue)
                    {
                        dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                    }
                }

                maintenanceDepartments = availableMaintenanceDepts;
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"筛选维修部门失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadAllMaintenanceDepartments()
    {
        try
        {
            // 获取维修类型的部门作为可选择的维修部门
            var allMaintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();

            // 获取用户可以选择的维修部门（基于权限）
            var userReceivableDepartments = await RoleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(currentUserId);

            // 如果用户有接收权限，则只显示有权限的维修部门；否则显示所有维修部门
            if (userReceivableDepartments.Any())
            {
                maintenanceDepartments = allMaintenanceDepartments
                    .Where(md => userReceivableDepartments.Any(rd => rd.Id == md.Id))
                    .ToList();
            }
            else
            {
                // 如果用户没有特定的接收权限，显示所有维修部门
                maintenanceDepartments = allMaintenanceDepartments;
            }

            // 加载部门类型信息
            foreach (var dept in maintenanceDepartments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载维修部门失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadMaintenancePersonnel()
    {
        try
        {
            Logger.LogInformation("[CreateRepairOrder] 开始加载维修人员，维修部门ID: {MaintenanceDepartmentId}", repairOrder.MaintenanceDepartmentId);

            if (repairOrder.MaintenanceDepartmentId > 0)
            {
                // 直接从选择的维修部门下面具有维修工种的用户中筛选
                availablePersonnel = await JobTypeService.GetMaintenanceUsersByDepartmentAsync(repairOrder.MaintenanceDepartmentId);

                Logger.LogInformation("[CreateRepairOrder] 获取到 {Count} 个维修人员", availablePersonnel.Count);
            }
            else
            {
                Logger.LogInformation("[CreateRepairOrder] 维修部门ID为0，清空维修人员列表");
                availablePersonnel.Clear();
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[CreateRepairOrder] 加载维修人员失败");
            Snackbar.Add($"加载维修人员失败: {ex.Message}", Severity.Error);
        }
    }

    private Color GetEquipmentStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Success, // 正常
            2 => Color.Warning, // 维修中
            3 => Color.Default, // 停用
            4 => Color.Error, // 报废
            _ => Color.Default
        };
    }

    private string GetMaintenanceDepartmentHelperText()
    {
        if (selectedEquipment != null)
        {
            return $"已根据设备所属部门({selectedEquipment.DepartmentName})自动筛选可用维修部门";
        }
        return "请先选择设备，系统将自动筛选可用维修部门";
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/repair-order-management");
    }

    private async Task SubmitRepairOrder()
    {
        // 验证用户权限
        if (!hasReportPermission)
        {
            Snackbar.Add("您没有设备报修权限", Severity.Error);
            return;
        }

        // 验证选择的设备是否在用户有权限的部门内
        if (selectedEquipment != null)
        {
            var canReport = await RoleDepartmentAssignmentService.CanUserAccessDepartmentAsync(currentUserId, selectedEquipment.DepartmentId);
            if (!canReport)
            {
                Snackbar.Add("您没有权限报修该部门的设备", Severity.Error);
                return;
            }
        }

        // 验证选择的维修部门是否有权限维修该设备所属部门
        if (selectedEquipment != null && repairOrder.MaintenanceDepartmentId > 0)
        {
            var canMaintain = await MaintenanceDepartmentPermissionService.CanMaintenanceDepartmentRepairAsync(repairOrder.MaintenanceDepartmentId, selectedEquipment.DepartmentId);
            if (!canMaintain)
            {
                Snackbar.Add("选择的维修部门没有权限维修该设备所属部门的设备", Severity.Error);
                return;
            }
        }

        await form.Validate();
        if (!form.IsValid) return;

        submitting = true;
        try
        {
            // 如果用户指定了维修人员，设置到报修单
            if (selectedMaintenancePersonnelId.HasValue)
            {
                repairOrder.AssignedTo = selectedMaintenancePersonnelId.Value;
            }
            else if (selectedEquipment != null && availablePersonnel.Any())
            {
                // 简单分配：选择第一个可用的维修人员
                var firstAvailablePersonnel = availablePersonnel.First();
                repairOrder.AssignedTo = firstAvailablePersonnel.Id;
                Snackbar.Add($"已自动分配维修人员：{firstAvailablePersonnel.DisplayName}", Severity.Info);
            }

            var result = await RepairOrderService.CreateRepairOrderAsync(repairOrder);
            if (result.IsSuccess)
            {
                Snackbar.Add($"报修单提交成功！报修单号：{result.OrderNumber}", Severity.Success);
                Navigation.NavigateTo("/repair-order-management");
            }
            else
            {
                Snackbar.Add($"提交失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"提交失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            submitting = false;
        }
    }

    public class RepairOrderValidator : AbstractValidator<RepairOrder>
    {
        public RepairOrderValidator()
        {
            RuleFor(x => x.EquipmentId)
            .GreaterThan(0).WithMessage("请选择设备");

            RuleFor(x => x.FaultDescription)
            .NotEmpty().WithMessage("请描述故障现象")
            .MinimumLength(10).WithMessage("故障描述至少需要10个字符")
            .MaximumLength(1000).WithMessage("故障描述不能超过1000个字符");

            RuleFor(x => x.UrgencyLevel)
            .InclusiveBetween(1, 4).WithMessage("请选择紧急程度");

            RuleFor(x => x.MaintenanceDepartmentId)
            .GreaterThan(0).WithMessage("请选择维修部门");

            RuleFor(x => x.Remark)
            .MaximumLength(1000).WithMessage("补充说明不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<RepairOrder>.CreateWithOptions((RepairOrder)model, x =>
    x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
