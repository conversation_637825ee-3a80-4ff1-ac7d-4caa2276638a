@page "/location-management"
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@inject ILocationService LocationService
@inject IDepartmentService DepartmentService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>位置管理</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.LocationOn" Class="mr-2" />
                    位置管理
                </MudText>
            </MudItem>

            <!-- 工具栏 -->
            <MudItem xs="12">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索位置名称、编码或地址..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnSearchKeyUp"
                                    Class="mr-4" />
                        <MudSelect T="int?" @bind-Value="selectedDepartmentId"
                                 Label="所属部门"
                                 Clearable="true"
                                 OnSelectionChanged="OnDepartmentChanged"
                                 Class="mr-4">
                            @foreach (var dept in departments)
                            {
                                <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name</MudSelectItem>
                            }
                        </MudSelect>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Refresh"
                                 OnClick="LoadLocations">
                            刷新
                        </MudButton>
                    </MudStack>
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Primary" 
                             StartIcon="@Icons.Material.Filled.Add"
                             OnClick="OpenCreateDialog">
                        新增位置
                    </MudButton>
                </MudStack>
            </MudItem>

            <!-- 数据表格 -->
            <MudItem xs="12">
                <MudDataGrid T="LocationDetailDto" 
                           Items="@filteredLocations" 
                           Loading="@loading"
                           Hover="true" 
                           Striped="true"
                           Dense="true"
                           FixedHeader="true"
                           Height="600px">
                    <Columns>
                        <PropertyColumn Property="x => x.Code" Title="位置编码" />
                        <PropertyColumn Property="x => x.Name" Title="位置名称" />
                        <PropertyColumn Property="x => x.DepartmentName" Title="所属部门" />
                        <PropertyColumn Property="x => x.ParentName" Title="上级位置" />
                        <PropertyColumn Property="x => x.Level" Title="级别" />
                        <PropertyColumn Property="x => x.Address" Title="详细地址" />
                        <PropertyColumn Property="x => x.EquipmentCount" Title="设备数量" />
                        <TemplateColumn Title="状态" Sortable="false">
                            <CellTemplate>
                                <MudChip Color="@(context.Item.IsEnabled ? Color.Success : Color.Default)" 
                                       Size="Size.Small">
                                    @(context.Item.IsEnabled ? "启用" : "禁用")
                                </MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <PropertyColumn Property="x => x.CreatedAt" Title="创建时间" Format="yyyy-MM-dd HH:mm" />
                        <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                            <CellTemplate>
                                <MudStack Row Spacing="1">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenEditDialog(context.Item)"
                                                 Title="编辑" />
                                    <MudIconButton Icon="@(context.Item.IsEnabled ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                                 Color="@(context.Item.IsEnabled ? Color.Warning : Color.Success)" 
                                                 Size="Size.Small"
                                                 OnClick="() => ToggleStatus(context.Item)"
                                                 Title="@(context.Item.IsEnabled ? "禁用" : "启用")" />
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                 Color="Color.Error" 
                                                 Size="Size.Small"
                                                 OnClick="() => DeleteLocation(context.Item)"
                                                 Title="删除" />
                                </MudStack>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            暂无数据
                        </MudText>
                    </NoRecordsContent>
                    <LoadingContent>
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </LoadingContent>
                </MudDataGrid>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private List<LocationDetailDto> locations = new();
    private List<LocationDetailDto> filteredLocations = new();
    private List<Department> departments = new();
    private bool loading = false;
    private string searchText = string.Empty;
    private int? selectedDepartmentId = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
        await LoadLocations();
    }

    private async Task LoadDepartments()
    {
        try
        {
            departments = await DepartmentService.GetEnabledDepartmentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadLocations()
    {
        loading = true;
        try
        {
            locations = await LocationService.GetLocationDetailsAsync();
            FilterLocations();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载位置数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private void FilterLocations()
    {
        var filtered = locations.AsEnumerable();

        // 按搜索文本过滤
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            filtered = filtered.Where(l => 
                l.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                l.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (l.Address?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                l.DepartmentName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                (l.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            );
        }

        // 按部门过滤
        if (selectedDepartmentId.HasValue)
        {
            filtered = filtered.Where(l => l.DepartmentId == selectedDepartmentId.Value);
        }

        filteredLocations = filtered.ToList();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        FilterLocations();
    }

    private void OnDepartmentChanged(int? departmentId)
    {
        selectedDepartmentId = departmentId;
        FilterLocations();
    }

    private async Task OpenCreateDialog()
    {
        var parameters = new DialogParameters<LocationEditDialog>
        {
            { x => x.Location, new Location() },
            { x => x.IsEdit, false }
        };

        var dialog = await DialogService.ShowAsync<LocationEditDialog>("新增位置", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadLocations();
        }
    }

    private async Task OpenEditDialog(LocationDetailDto locationDetail)
    {
        // 获取完整的位置对象
        var location = await LocationService.GetLocationByIdAsync(locationDetail.Id);
        if (location == null)
        {
            Snackbar.Add("位置不存在", Severity.Error);
            return;
        }

        var parameters = new DialogParameters<LocationEditDialog>
        {
            { x => x.Location, location },
            { x => x.IsEdit, true }
        };

        var dialog = await DialogService.ShowAsync<LocationEditDialog>("编辑位置", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadLocations();
        }
    }

    private async Task ToggleStatus(LocationDetailDto locationDetail)
    {
        try
        {
            var result = await LocationService.ToggleStatusAsync(locationDetail.Id);
            if (result.IsSuccess)
            {
                Snackbar.Add($"位置状态已{(locationDetail.IsEnabled ? "禁用" : "启用")}", Severity.Success);
                await LoadLocations();
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeleteLocation(LocationDetailDto locationDetail)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除位置 '{locationDetail.Name}' 吗？此操作不可撤销。",
            yesText: "删除",
            cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await LocationService.DeleteLocationAsync(locationDetail.Id);
                if (result.IsSuccess)
                {
                    Snackbar.Add("位置删除成功", Severity.Success);
                    await LoadLocations();
                }
                else
                {
                    Snackbar.Add($"删除失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }
}
