@using MudBlazor

<MudDialog>
    <DialogContent>
        <div style="max-height: 600px; overflow-y: auto;">
            <MudTextField @bind-Value="searchTerm" 
                         Label="搜索图标" 
                         Placeholder="输入关键词搜索图标"
                         Adornment="Adornment.End"
                         AdornmentIcon="@Icons.Material.Filled.Search"
                         Immediate="true"
                         OnAdornmentClick="SearchIcons"
                         Margin="Margin.Dense"
                         Class="mb-4" />
            
            <MudGrid>
                @foreach (var iconInfo in filteredIcons)
                {
                    <MudItem xs="6" sm="4" md="3" lg="2">
                        <MudCard Class="icon-card" Style="cursor: pointer; text-align: center; padding: 8px; min-height: 100px;"
                                 @onclick="() => SelectIcon(iconInfo.FullPath)">
                            <MudCardContent Style="padding: 8px;">
                                <MudIcon Icon="@iconInfo.FullPath" Size="Size.Large" Style="margin-bottom: 8px;" />
                                <MudText Typo="Typo.caption" Style="font-size: 10px; line-height: 1.2;">
                                    @iconInfo.DisplayName
                                </MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                }
            </MudGrid>
            
            @if (!filteredIcons.Any())
            {
                <div style="text-align: center; padding: 40px;">
                    <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Default" />
                    <MudText Typo="Typo.body1" Color="Color.Default">未找到匹配的图标</MudText>
                </div>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
    </DialogActions>
</MudDialog>

<style>
    .icon-card:hover {
        background-color: var(--mud-palette-action-hover);
        transform: scale(1.05);
        transition: all 0.2s ease-in-out;
    }
</style>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public EventCallback<string> OnIconSelected { get; set; }

    private string searchTerm = "";
    private List<IconInfo> allIcons = new();
    private List<IconInfo> filteredIcons = new();

    private class IconInfo
    {
        public string Name { get; set; } = "";
        public string FullPath { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public string Category { get; set; } = "";
    }

    protected override void OnInitialized()
    {
        LoadIcons();
        filteredIcons = allIcons.Take(50).ToList();
    }

    private void LoadIcons()
    {
        allIcons = new List<IconInfo>
        {
            new() { Name = "Home", FullPath = Icons.Material.Filled.Home, DisplayName = "首页", Category = "Navigation" },
            new() { Name = "Dashboard", FullPath = Icons.Material.Filled.Dashboard, DisplayName = "仪表板", Category = "Navigation" },
            new() { Name = "Settings", FullPath = Icons.Material.Filled.Settings, DisplayName = "设置", Category = "Action" },
            new() { Name = "People", FullPath = Icons.Material.Filled.People, DisplayName = "用户", Category = "Social" },
            new() { Name = "Person", FullPath = Icons.Material.Filled.Person, DisplayName = "个人", Category = "Social" },
            new() { Name = "Security", FullPath = Icons.Material.Filled.Security, DisplayName = "安全", Category = "Action" },
            new() { Name = "Key", FullPath = Icons.Material.Filled.Key, DisplayName = "密钥", Category = "Communication" },
            new() { Name = "Lock", FullPath = Icons.Material.Filled.Lock, DisplayName = "锁定", Category = "Action" },
            new() { Name = "Menu", FullPath = Icons.Material.Filled.Menu, DisplayName = "菜单", Category = "Navigation" },
            new() { Name = "Add", FullPath = Icons.Material.Filled.Add, DisplayName = "添加", Category = "Content" },
            new() { Name = "Edit", FullPath = Icons.Material.Filled.Edit, DisplayName = "编辑", Category = "Content" },
            new() { Name = "Delete", FullPath = Icons.Material.Filled.Delete, DisplayName = "删除", Category = "Action" },
            new() { Name = "Build", FullPath = Icons.Material.Filled.Build, DisplayName = "构建", Category = "Action" },
            new() { Name = "QrCodeScanner", FullPath = Icons.Material.Filled.QrCodeScanner, DisplayName = "二维码扫描", Category = "Action" },
            new() { Name = "Camera", FullPath = Icons.Material.Filled.Camera, DisplayName = "相机", Category = "Image" },
            new() { Name = "WbSunny", FullPath = Icons.Material.Filled.WbSunny, DisplayName = "晴天", Category = "Image" },
            new() { Name = "Storage", FullPath = Icons.Material.Filled.Storage, DisplayName = "存储", Category = "Device" },
            new() { Name = "Shield", FullPath = Icons.Material.Filled.Shield, DisplayName = "盾牌", Category = "Action" },
            new() { Name = "BugReport", FullPath = Icons.Material.Filled.BugReport, DisplayName = "错误报告", Category = "Action" },
            new() { Name = "Code", FullPath = Icons.Material.Filled.Code, DisplayName = "代码", Category = "Action" },
            new() { Name = "PersonAdd", FullPath = Icons.Material.Filled.PersonAdd, DisplayName = "添加人员", Category = "Social" },
            new() { Name = "Login", FullPath = Icons.Material.Filled.Login, DisplayName = "登录", Category = "Action" },
            new() { Name = "Save", FullPath = Icons.Material.Filled.Save, DisplayName = "保存", Category = "Content" },
            new() { Name = "Search", FullPath = Icons.Material.Filled.Search, DisplayName = "搜索", Category = "Action" }
        };
    }

    private void SearchIcons()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredIcons = allIcons.Take(50).ToList();
        }
        else
        {
            var searchLower = searchTerm.ToLowerInvariant();
            filteredIcons = allIcons
                .Where(icon => 
                    icon.DisplayName.ToLowerInvariant().Contains(searchLower) ||
                    icon.Name.ToLowerInvariant().Contains(searchLower) ||
                    icon.Category.ToLowerInvariant().Contains(searchLower))
                .Take(100)
                .ToList();
        }
        StateHasChanged();
    }

    private void SelectIcon(string iconPath)
    {
        OnIconSelected.InvokeAsync(iconPath);
        MudDialog.Close(DialogResult.Ok(iconPath));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
} 