using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 报修单附件实体
    /// </summary>
    [SugarTable("RepairOrderAttachments")]
    public class RepairOrderAttachment
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 报修单ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "报修单不能为空")]
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        [Required(ErrorMessage = "文件名不能为空")]
        [StringLength(255, ErrorMessage = "文件名长度不能超过255个字符")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 原始文件名
        /// </summary>
        [SugarColumn(Length = 255, IsNullable = false)]
        [Required(ErrorMessage = "原始文件名不能为空")]
        [StringLength(255, ErrorMessage = "原始文件名长度不能超过255个字符")]
        public string OriginalFileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = false)]
        [Required(ErrorMessage = "文件路径不能为空")]
        [StringLength(500, ErrorMessage = "文件路径长度不能超过500个字符")]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public long FileSize { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "文件类型不能为空")]
        [StringLength(100, ErrorMessage = "文件类型长度不能超过100个字符")]
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// 附件类型（1=故障图片,2=维修图片,3=其他文档）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int AttachmentType { get; set; } = 1;

        /// <summary>
        /// 上传人ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "上传人不能为空")]
        public int UploadedBy { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "描述长度不能超过500个字符")]
        public string? Description { get; set; }

        // 导航属性
        /// <summary>
        /// 报修单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public RepairOrder? RepairOrder { get; set; }

        /// <summary>
        /// 附件类型名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AttachmentTypeName => AttachmentType switch
        {
            1 => "故障图片",
            2 => "维修图片",
            3 => "其他文档",
            _ => "未知"
        };

        /// <summary>
        /// 文件大小显示
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FileSizeDisplay
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F1} KB";
                else if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024.0 * 1024.0):F1} MB";
                else
                    return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }
    }
}
