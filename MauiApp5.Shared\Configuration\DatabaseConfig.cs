namespace MauiApp5.Shared.Configuration
{
    /// <summary>
    /// 统一的数据库配置管理
    /// 支持环境变量覆盖，便于不同环境部署
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// 默认数据库连接字符串 (企业管理系统数据库，包含权限管理和设备管理模块)
        /// 可以通过环境变量 DB_CONNECTION_STRING 覆盖
        /// </summary>
        public static string DefaultConnectionString =>
            Environment.GetEnvironmentVariable("DB_CONNECTION_STRING") ??
            "Server=***********;Database=EnterpriseManagementSystem;User Id=sa;Password=****;TrustServerCertificate=True;Connect Timeout=30;MultipleActiveResultSets=True";

        /// <summary>
        /// 是否使用存储过程认证
        /// 可以通过环境变量 USE_STORED_PROCEDURE 覆盖
        /// </summary>
        public static bool UseStoredProcedure =>
            bool.TryParse(Environment.GetEnvironmentVariable("USE_STORED_PROCEDURE"), out var result) ? result : true;

        /// <summary>
        /// 获取配置字典（用于内存配置）
        /// </summary>
        public static Dictionary<string, string?> GetConfigurationData()
        {
            return new Dictionary<string, string?>
            {
                ["ConnectionStrings:DefaultConnection"] = DefaultConnectionString,
                ["AuthenticationSettings:UseStoredProcedure"] = UseStoredProcedure.ToString()
            };
        }

        /// <summary>
        /// 显示当前配置信息（用于调试）
        /// </summary>
        public static void LogCurrentConfig(Action<string> logger)
        {
            logger($"数据库服务器: {GetServerFromConnectionString()}");
            logger($"数据库名称: {GetDatabaseFromConnectionString()}");
            logger($"使用存储过程: {UseStoredProcedure}");
            logger($"配置来源: {(HasEnvironmentOverrides() ? "环境变量" : "默认配置")}");
        }

        private static string GetServerFromConnectionString()
        {
            var connStr = DefaultConnectionString;
            var serverStart = connStr.IndexOf("Server=") + 7;
            var serverEnd = connStr.IndexOf(";", serverStart);
            return serverEnd > serverStart ? connStr.Substring(serverStart, serverEnd - serverStart) : "未知";
        }

        private static string GetDatabaseFromConnectionString()
        {
            var connStr = DefaultConnectionString;
            var dbStart = connStr.IndexOf("Database=") + 9;
            var dbEnd = connStr.IndexOf(";", dbStart);
            return dbEnd > dbStart ? connStr.Substring(dbStart, dbEnd - dbStart) : "未知";
        }

        private static bool HasEnvironmentOverrides()
        {
            return !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("DB_CONNECTION_STRING")) ||
                   !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("USE_STORED_PROCEDURE"));
        }
    }
} 