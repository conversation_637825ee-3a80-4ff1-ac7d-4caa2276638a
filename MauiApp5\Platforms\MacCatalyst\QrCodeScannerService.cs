using MauiApp5.Shared.Services;
using ZXing.Net.Maui;
using Microsoft.Maui.Controls;
using ZXing.Net.Maui.Controls;

namespace MauiApp5.Platforms.MacCatalyst
{
    public class QrCodeScannerService : IQrCodeScannerService
    {
        private TaskCompletionSource<string?>? _scanTaskCompletionSource;

        public async Task<string?> ScanQrCodeAsync()
        {
            try
            {
                // 检查权限
                if (!await RequestCameraPermissionAsync())
                {
                    return null;
                }

                // 检查摄像头是否可用
                if (!IsCameraAvailable())
                {
                    return null;
                }

                // 创建扫描任务
                _scanTaskCompletionSource = new TaskCompletionSource<string?>();

                // 在主线程上启动扫描页面
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        // 创建扫描页面
                        var scanPage = new ContentPage
                        {
                            Title = "扫描二维码",
                            BackgroundColor = Colors.Black
                        };

                        var cameraView = new CameraBarcodeReaderView
                        {
                            IsDetecting = true,
                            CameraLocation = CameraLocation.Rear,
                            VerticalOptions = LayoutOptions.FillAndExpand,
                            HorizontalOptions = LayoutOptions.FillAndExpand,
                            // 添加条码格式支持
                            Options = new BarcodeReaderOptions
                            {
                                Formats = BarcodeFormats.All,
                                AutoRotate = true,
                                Multiple = false,
                                TryHarder = true,
                                TryInverted = true
                            }
                        };

                        // 处理扫描结果
                        cameraView.BarcodesDetected += (sender, e) =>
                        {
                            if (e.Results?.Any() == true)
                            {
                                var result = e.Results.First().Value;
                                System.Diagnostics.Debug.WriteLine($"扫描到条码: {result}");
                                MainThread.BeginInvokeOnMainThread(async () =>
                                {
                                    await Application.Current?.MainPage?.Navigation.PopModalAsync();
                                    _scanTaskCompletionSource?.SetResult(result);
                                });
                            }
                        };

                        // 创建取消按钮
                        var cancelButton = new Button
                        {
                            Text = "取消扫描",
                            BackgroundColor = Colors.Red,
                            TextColor = Colors.White,
                            Margin = new Thickness(20),
                            VerticalOptions = LayoutOptions.End
                        };

                        cancelButton.Clicked += async (sender, e) =>
                        {
                            await Application.Current?.MainPage?.Navigation.PopModalAsync();
                            _scanTaskCompletionSource?.SetResult(null);
                        };

                        // 创建标题标签
                        var titleLabel = new Label
                        {
                            Text = "请将二维码对准摄像头",
                            TextColor = Colors.White,
                            FontSize = 18,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = new Thickness(20, 40, 20, 20)
                        };

                        // 创建扫描框指示器
                        var scanFrame = new Frame
                        {
                            BackgroundColor = Colors.Transparent,
                            BorderColor = Colors.Red,
                            HasShadow = false,
                            WidthRequest = 250,
                            HeightRequest = 250,
                            HorizontalOptions = LayoutOptions.Center,
                            VerticalOptions = LayoutOptions.Center,
                            Content = new Label
                            {
                                Text = "扫描区域",
                                TextColor = Colors.White,
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Center
                            }
                        };

                        // 创建网格布局
                        var grid = new Grid
                        {
                            RowDefinitions = 
                            {
                                new RowDefinition { Height = GridLength.Auto },
                                new RowDefinition { Height = GridLength.Star },
                                new RowDefinition { Height = GridLength.Auto }
                            },
                            BackgroundColor = Colors.Black
                        };

                        // 添加控件到网格
                        grid.Add(titleLabel, 0, 0);
                        grid.Add(cameraView, 0, 1);
                        grid.Add(scanFrame, 0, 1); // 覆盖在摄像头视图上
                        grid.Add(cancelButton, 0, 2);

                        scanPage.Content = grid;

                        // 使用模态页面推送扫描页面
                        await Application.Current?.MainPage?.Navigation.PushModalAsync(scanPage);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"启动扫描页面失败: {ex.Message}");
                        _scanTaskCompletionSource?.SetResult(null);
                    }
                });

                // 等待扫描结果
                var result = await _scanTaskCompletionSource.Task;
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"扫描二维码失败: {ex.Message}");
                _scanTaskCompletionSource?.SetResult(null);
                return null;
            }
        }

        public async Task<bool> RequestCameraPermissionAsync()
        {
            try
            {
                var status = await Permissions.RequestAsync<Permissions.Camera>();
                return status == PermissionStatus.Granted;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"请求摄像头权限失败: {ex.Message}");
                return false;
            }
        }

        public bool IsCameraAvailable()
        {
            try
            {
                return UIKit.UIImagePickerController.IsSourceTypeAvailable(UIKit.UIImagePickerControllerSourceType.Camera);
            }
            catch
            {
                return false;
            }
        }
    }
} 