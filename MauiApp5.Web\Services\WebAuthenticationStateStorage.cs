using Microsoft.JSInterop;
using MauiApp5.Shared.Services;

namespace MauiApp5.Web.Services
{
    /// <summary>
    /// Web平台认证状态存储实现 - 使用localStorage
    /// </summary>
    public class WebAuthenticationStateStorage : IAuthenticationStateStorage
    {
        private readonly IJSRuntime _jsRuntime;

        public WebAuthenticationStateStorage(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public bool IsAvailable => true;

        public async Task SaveAuthStateAsync(string key, string data)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", key, data);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Web存储保存失败: {ex.Message}");
                throw;
            }
        }

        public async Task<string?> GetAuthStateAsync(string key)
        {
            try
            {
                return await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", key);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Web存储读取失败: {ex.Message}");
                return null;
            }
        }

        public async Task ClearAuthStateAsync(string key)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", key);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Web存储清除失败: {ex.Message}");
            }
        }
    }
} 