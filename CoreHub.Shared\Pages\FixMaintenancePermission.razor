@page "/fix-maintenance-permission"
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Data
@inject IRoleDepartmentPermissionService RoleDepartmentPermissionService
@inject DatabaseContext DbContext
@inject ISnackbar Snackbar

<PageTitle>修复维修权限</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">修复操作员维修权限</MudText>
    
    <MudCard>
        <MudCardContent>
            <MudText Typo="Typo.body1" Class="mb-4">
                这个页面用于修复操作员角色缺少维修权限的问题，导致维修仪表板没有数据。
            </MudText>
            
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="AddMaintenancePermission">
                添加操作员维修权限
            </MudButton>
            
            <MudButton Variant="Variant.Outlined" Color="Color.Secondary" OnClick="CheckPermissions" Class="ml-2">
                检查当前权限
            </MudButton>
        </MudCardContent>
    </MudCard>
    
    @if (permissionResults.Any())
    {
        <MudCard Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">操作员角色权限列表</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="permissionResults" Hover="true" Striped="true">
                    <HeaderContent>
                        <MudTh>部门名称</MudTh>
                        <MudTh>权限类型</MudTh>
                        <MudTh>权限名称</MudTh>
                        <MudTh>是否启用</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="部门名称">@context.DepartmentName</MudTd>
                        <MudTd DataLabel="权限类型">@context.PermissionType</MudTd>
                        <MudTd DataLabel="权限名称">@context.PermissionTypeName</MudTd>
                        <MudTd DataLabel="是否启用">
                            <MudChip T="string" Color="@(context.IsEnabled ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsEnabled ? "启用" : "禁用")
                            </MudChip>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private List<PermissionResult> permissionResults = new();
    
    private class PermissionResult
    {
        public string DepartmentName { get; set; } = "";
        public int PermissionType { get; set; }
        public string PermissionTypeName { get; set; } = "";
        public bool IsEnabled { get; set; }
    }
    
    protected override async Task OnInitializedAsync()
    {
        await CheckPermissions();
    }
    
    private async Task AddMaintenancePermission()
    {
        try
        {
            // 获取操作员角色和整理部
            var operatorRole = await DbContext.Db.Queryable<Role>()
                .Where(r => r.Code == "Operator")
                .FirstAsync();
                
            var zlbDepartment = await DbContext.Db.Queryable<Department>()
                .Where(d => d.Code == "ZLB")
                .FirstAsync();
                
            if (operatorRole == null || zlbDepartment == null)
            {
                Snackbar.Add("找不到操作员角色或整理部", Severity.Error);
                return;
            }
            
            // 检查权限是否已存在
            var existingPermission = await DbContext.Db.Queryable<RoleDepartmentPermission>()
                .Where(rdp => rdp.RoleId == operatorRole.Id && 
                             rdp.DepartmentId == zlbDepartment.Id && 
                             rdp.PermissionType == 3)
                .FirstAsync();
                
            if (existingPermission != null)
            {
                Snackbar.Add("操作员维修权限已存在", Severity.Info);
                return;
            }
            
            // 添加维修权限
            var newPermission = new RoleDepartmentPermission
            {
                RoleId = operatorRole.Id,
                DepartmentId = zlbDepartment.Id,
                PermissionType = 3, // 可维修设备
                IsEnabled = true
            };
            
            await DbContext.Db.Insertable(newPermission).ExecuteCommandAsync();
            
            Snackbar.Add("成功添加操作员维修权限", Severity.Success);
            
            // 刷新权限列表
            await CheckPermissions();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"添加权限失败: {ex.Message}", Severity.Error);
        }
    }
    
    private async Task CheckPermissions()
    {
        try
        {
            var results = await DbContext.Db.Queryable<RoleDepartmentPermission>()
                .LeftJoin<Role>((rdp, r) => rdp.RoleId == r.Id)
                .LeftJoin<Department>((rdp, r, d) => rdp.DepartmentId == d.Id)
                .Where((rdp, r, d) => r.Code == "Operator")
                .Select((rdp, r, d) => new PermissionResult
                {
                    DepartmentName = d.Name,
                    PermissionType = rdp.PermissionType,
                    PermissionTypeName = rdp.PermissionType == 1 ? "可报修设备" :
                                       rdp.PermissionType == 2 ? "可接收报修" :
                                       rdp.PermissionType == 3 ? "可维修设备" : "未知权限",
                    IsEnabled = rdp.IsEnabled
                })
                .ToListAsync();
                
            permissionResults = results;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"检查权限失败: {ex.Message}", Severity.Error);
        }
    }
}
