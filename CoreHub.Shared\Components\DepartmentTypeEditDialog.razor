@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@using FluentValidation
@inject IDepartmentTypeService DepartmentTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@DepartmentType" Validation="@(new DepartmentTypeValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="DepartmentType.Code"
                                For="@(() => DepartmentType.Code)"
                                Label="类型编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="DepartmentType.Name"
                                For="@(() => DepartmentType.Name)"
                                Label="类型名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="DepartmentType.Description"
                                For="@(() => DepartmentType.Description)"
                                Label="描述"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="DepartmentType.SortOrder"
                                   For="@(() => DepartmentType.SortOrder)"
                                   Label="排序号"
                                   Min="0"
                                   Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSwitch T="bool" @bind-Checked="DepartmentType.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public DepartmentType DepartmentType { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;

    protected override async Task OnInitializedAsync()
    {
        if (!IsEdit)
        {
            DepartmentType.SortOrder = 0;
            DepartmentType.IsEnabled = true;
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            // 检查编码是否重复
            if (!IsEdit || !string.Equals(DepartmentType.Code, originalCode, StringComparison.OrdinalIgnoreCase))
            {
                var exists = await DepartmentTypeService.ExistsByCodeAsync(DepartmentType.Code, IsEdit ? DepartmentType.Id : null);
                if (exists)
                {
                    Snackbar.Add("部门类型编码已存在", Severity.Error);
                    return;
                }
            }

            bool result;
            
            if (IsEdit)
            {
                DepartmentType.UpdatedAt = DateTime.Now;
                result = await DepartmentTypeService.UpdateDepartmentTypeAsync(DepartmentType);
            }
            else
            {
                result = await DepartmentTypeService.CreateDepartmentTypeAsync(DepartmentType);
            }

            if (result)
            {
                Snackbar.Add($"部门类型{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    private string originalCode = string.Empty;

    protected override void OnParametersSet()
    {
        if (IsEdit)
        {
            originalCode = DepartmentType.Code;
        }
    }

    public class DepartmentTypeValidator : AbstractValidator<DepartmentType>
    {
        public DepartmentTypeValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("类型编码不能为空")
                .MaximumLength(50).WithMessage("类型编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("类型名称不能为空")
                .MaximumLength(100).WithMessage("类型名称长度不能超过100个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("描述长度不能超过500个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<DepartmentType>.CreateWithOptions((DepartmentType)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
