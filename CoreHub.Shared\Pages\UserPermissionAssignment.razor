@page "/user-permissions"
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Components
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar

<PageTitle>用户权限分配</PageTitle>

<PermissionView RequiredPermission="UserManagement.AssignRoles">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
            <MudPaper Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-2">用户权限分配</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default">为用户分配角色和直接权限，实现精细化权限控制</MudText>
            </MudPaper>

            <MudGrid>
                <!-- 左侧：用户列表 -->
                <MudItem xs="12" md="4">
                    <MudPaper Elevation="2" Class="pa-4">
                        <div style="display: flex; align-items: center; margin-bottom: 16px;">
                            <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-2" />
                            <MudText Typo="Typo.h6">用户列表</MudText>
                        </div>
                        
                        <MudTextField @bind-Value="userSearchKeyword"
                                      @onkeypress="OnUserSearchKeyPress"
                                      Label="搜索用户"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      Class="mb-3" />
                        
                        @if (isLoadingUsers)
                        {
                            <div style="display: flex; justify-content: center; padding: 20px;">
                                <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                            </div>
                        }
                        else
                        {
                            <div style="max-height: 400px; overflow-y: auto;">
                                @foreach (var user in filteredUsers)
                                {
                                    <MudCard Class="@GetUserCardClass(user)" 
                                             Style="@GetUserCardStyle(user)"
                                             @onclick="() => SelectUser(user)">
                                        <MudCardContent Class="pa-3">
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <MudText Typo="Typo.h6">@user.DisplayName</MudText>
                                                <MudText Typo="Typo.caption">@user.Username</MudText>
                                            </div>
                                            <MudText Typo="Typo.body2" Class="mb-1">@user.Email</MudText>
                                            <div style="display: flex; gap: 4px;">
                                                @if (user.IsEnabled)
                                                {
                                                    <MudChip T="string" Color="Color.Success" Size="Size.Small">已启用</MudChip>
                                                }
                                                else
                                                {
                                                    <MudChip T="string" Color="Color.Error" Size="Size.Small">已禁用</MudChip>
                                                }
                                                @if (user.IsLocked)
                                                {
                                                    <MudChip T="string" Color="Color.Warning" Size="Size.Small">已锁定</MudChip>
                                                }
                                            </div>
                                        </MudCardContent>
                                    </MudCard>
                                }
                            </div>
                        }
                    </MudPaper>
                </MudItem>

                <!-- 右侧：权限配置 -->
                <MudItem xs="12" md="8">
                    @if (selectedUser != null)
                    {
                        <MudPaper Elevation="2" Class="pa-4">
                            <div style="display: flex; align-items: center; margin-bottom: 16px;">
                                <MudIcon Icon="@Icons.Material.Filled.Settings" Class="mr-2" />
                                <MudText Typo="Typo.h6">@selectedUser.DisplayName 的权限配置</MudText>
                            </div>

                            <!-- 角色分配 -->
                            <MudExpansionPanels MultiExpansion="true" Class="mb-4">
                                <MudExpansionPanel>
                                    <TitleContent>
                                        <div style="display: flex; align-items: center;">
                                            <MudIcon Icon="@Icons.Material.Filled.Group" class="mr-3" />
                                            <MudText Typo="Typo.h6">角色分配</MudText>
                                            <MudSpacer />
                                            <MudButton Size="Size.Small"
                                                       Color="Color.Primary"
                                                       Variant="Variant.Filled"
                                                       Disabled="@isSavingRoles"
                                                       OnClick="SaveUserRoles">
                                                @if (isSavingRoles)
                                                {
                                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-1" />
                                                }
                                                保存角色
                                            </MudButton>
                                        </div>
                                    </TitleContent>
                                    <ChildContent>
                                        @if (isLoadingRoles)
                                        {
                                            <div style="display: flex; justify-content: center; padding: 20px;">
                                                <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                                            </div>
                                        }
                                        else
                                        {
                                            <MudGrid>
                                                @foreach (var role in allRoles)
                                                {
                                                    <MudItem xs="12" md="6">
                                                        <MudCheckBox Value="@userRoles.Contains(role.Id)"
                                                                     ValueChanged="@((bool value) => ToggleRole(role.Id, value))"
                                                                     Label="@role.Name"
                                                                     Color="Color.Primary" />
                                                        <MudText Typo="Typo.caption" Color="Color.Default">@role.Description</MudText>
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                        }
                                    </ChildContent>
                                </MudExpansionPanel>

                                <!-- 直接权限分配 -->
                                <MudExpansionPanel>
                                    <TitleContent>
                                        <div style="display: flex; align-items: center;">
                                            <MudIcon Icon="@Icons.Material.Filled.Key" class="mr-3" />
                                            <MudText Typo="Typo.h6">直接权限</MudText>
                                            <MudSpacer />
                                            <MudButton Size="Size.Small"
                                                       Color="Color.Success"
                                                       Variant="Variant.Filled"
                                                       Disabled="@isSavingPermissions"
                                                       OnClick="SaveUserPermissions">
                                                @if (isSavingPermissions)
                                                {
                                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-1" />
                                                }
                                                保存权限
                                            </MudButton>
                                        </div>
                                    </TitleContent>
                                    <ChildContent>
                                        @if (isLoadingPermissions)
                                        {
                                            <div style="display: flex; justify-content: center; padding: 20px;">
                                                <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                                            </div>
                                        }
                                        else
                                        {
                                            <!-- 权限搜索 -->
                                            <MudTextField @bind-Value="permissionSearchKeyword"
                                                          @oninput="FilterPermissions"
                                                          Label="搜索权限"
                                                          Variant="Variant.Outlined"
                                                          Adornment="Adornment.End"
                                                          AdornmentIcon="@Icons.Material.Filled.Search"
                                                          Class="mb-3" />

                                            <!-- 按模块分组显示权限 -->
                                            <MudExpansionPanels MultiExpansion="true">
                                                @foreach (var module in filteredPermissionsByModule)
                                                {
                                                    <MudExpansionPanel>
                                                        <TitleContent>
                                                            <div style="display: flex; align-items: center;">
                                                                <MudIcon Icon="@Icons.Material.Filled.Category" class="mr-3" />
                                                                <MudText>@module.Key 模块 (@module.Value.Count 个权限)</MudText>
                                                            </div>
                                                        </TitleContent>
                                                        <ChildContent>
                                                            <MudGrid>
                                                                @foreach (var permission in module.Value)
                                                                {
                                                                    <MudItem xs="12" md="6">
                                                                        <div style="display: flex; align-items: flex-start; gap: 8px;">
                                                                            <MudCheckBox Value="@userDirectPermissions.Contains(permission.Id)"
                                                                                         ValueChanged="@((bool value) => TogglePermission(permission.Id, value))"
                                                                                         Color="Color.Primary" />
                                                                            <div style="flex: 1;">
                                                                                <MudText Typo="Typo.body2"><strong>@permission.Name</strong></MudText>
                                                                                <MudText Typo="Typo.caption" Color="Color.Default">@permission.Description</MudText>
                                                                                <MudChip T="string" Color="@GetLevelColor(permission.Level)" Size="Size.Small" Class="mt-1">
                                                                                    @GetLevelText(permission.Level)
                                                                                </MudChip>
                                                                            </div>
                                                                        </div>
                                                                    </MudItem>
                                                                }
                                                            </MudGrid>
                                                        </ChildContent>
                                                    </MudExpansionPanel>
                                                }
                                            </MudExpansionPanels>
                                        }
                                    </ChildContent>
                                </MudExpansionPanel>

                                <!-- 权限预览 -->
                                <MudExpansionPanel>
                                    <TitleContent>
                                        <div style="display: flex; align-items: center;">
                                            <MudIcon Icon="@Icons.Material.Filled.Visibility" class="mr-3" />
                                            <MudText Typo="Typo.h6">权限预览</MudText>
                                            <MudSpacer />
                                            <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                                          Size="Size.Small"
                                                          Color="Color.Default"
                                                          OnClick="RefreshUserPermissions" />
                                        </div>
                                    </TitleContent>
                                    <ChildContent>
                                        @if (isLoadingAllPermissions)
                                        {
                                            <div style="display: flex; justify-content: center; padding: 20px;">
                                                <MudProgressCircular Color="Color.Primary" Size="Size.Small" Indeterminate="true" />
                                            </div>
                                        }
                                        else
                                        {
                                            <MudGrid>
                                                <MudItem xs="12" md="6">
                                                    <MudText Typo="Typo.h6" Color="Color.Success" Class="mb-2">通过角色获得的权限</MudText>
                                                    @if (roleBasedPermissions.Any())
                                                    {
                                                        <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                                            @foreach (var permission in roleBasedPermissions.Take(10))
                                                            {
                                                                <MudChip T="string" Color="Color.Success" Size="Size.Small">@permission.Name</MudChip>
                                                            }
                                                        </div>
                                                        @if (roleBasedPermissions.Count > 10)
                                                        {
                                                            <MudText Typo="Typo.caption" Color="Color.Default" Class="mt-2">...等 @roleBasedPermissions.Count 个权限</MudText>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <MudText Color="Color.Default">无</MudText>
                                                    }
                                                </MudItem>
                                                
                                                <MudItem xs="12" md="6">
                                                    <MudText Typo="Typo.h6" Color="Color.Info" Class="mb-2">直接分配的权限</MudText>
                                                    @if (directPermissions.Any())
                                                    {
                                                        <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                                                            @foreach (var permission in directPermissions.Take(10))
                                                            {
                                                                <MudChip T="string" Color="Color.Info" Size="Size.Small">@permission.Name</MudChip>
                                                            }
                                                        </div>
                                                        @if (directPermissions.Count > 10)
                                                        {
                                                            <MudText Typo="Typo.caption" Color="Color.Default" Class="mt-2">...等 @directPermissions.Count 个权限</MudText>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <MudText Color="Color.Default">无</MudText>
                                                    }
                                                </MudItem>
                                            </MudGrid>
                                        }
                                    </ChildContent>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        </MudPaper>
                    }
                    else
                    {
                        <MudPaper Elevation="2" Class="pa-8" Style="text-align: center;">
                            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" 
                                     Size="Size.Large" 
                                     Color="Color.Default" 
                                     Style="font-size: 4rem; margin-bottom: 16px;" />
                            <MudText Typo="Typo.h5" Class="mb-2">请选择一个用户</MudText>
                            <MudText Typo="Typo.body1" Color="Color.Default">从左侧用户列表中选择一个用户来配置其权限</MudText>
                        </MudPaper>
                    }
                </MudItem>
            </MudGrid>
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudPaper Elevation="2" Class="pa-8" Style="text-align: center;">
                <MudIcon Icon="@Icons.Material.Filled.Lock" 
                         Size="Size.Large" 
                         Color="Color.Error" 
                         Style="font-size: 4rem; margin-bottom: 16px;" />
                <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">权限不足</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default">您没有权限访问用户权限分配页面</MudText>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</PermissionView>

@code {
    private List<User> allUsers = new();
    private List<User> filteredUsers = new();
    private List<Role> allRoles = new();
    private List<Permission> allPermissions = new();
    private Dictionary<string, List<Permission>> permissionsByModule = new();
    private Dictionary<string, List<Permission>> filteredPermissionsByModule = new();
    private HashSet<int> userRoles = new();
    private HashSet<int> userDirectPermissions = new();
    private List<Permission> roleBasedPermissions = new();
    private List<Permission> directPermissions = new();

    private bool isLoadingUsers = false;
    private bool isLoadingRoles = false;
    private bool isLoadingPermissions = false;
    private bool isLoadingAllPermissions = false;
    private bool isSavingRoles = false;
    private bool isSavingPermissions = false;

    private string userSearchKeyword = "";
    private string permissionSearchKeyword = "";

    private User? selectedUser;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        await LoadRoles();
        await LoadPermissions();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoadingUsers = true;
            StateHasChanged();

            var result = await UserManagementService.GetUsersAsync(1, 100);
            allUsers = result.Users;
            filteredUsers = allUsers;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingUsers = false;
            StateHasChanged();
        }
    }

    private async Task LoadRoles()
    {
        try
        {
            allRoles = await UserManagementService.GetAllRolesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载角色失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadPermissions()
    {
        try
        {
            allPermissions = await UserManagementService.GetAllPermissionsAsync();
            permissionsByModule = allPermissions
                .GroupBy(p => p.Module)
                .ToDictionary(g => g.Key, g => g.OrderBy(p => p.SortOrder).ToList());
            filteredPermissionsByModule = permissionsByModule;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载权限失败: {ex.Message}", Severity.Error);
        }
    }

    private void OnUserSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            FilterUsers();
        }
    }

    private void FilterUsers()
    {
        if (string.IsNullOrWhiteSpace(userSearchKeyword))
        {
            filteredUsers = allUsers;
        }
        else
        {
            filteredUsers = allUsers.Where(u =>
                u.Username.Contains(userSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                u.DisplayName.Contains(userSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                u.Email?.Contains(userSearchKeyword, StringComparison.OrdinalIgnoreCase) == true
            ).ToList();
        }
        StateHasChanged();
    }

    private void FilterPermissions()
    {
        if (string.IsNullOrWhiteSpace(permissionSearchKeyword))
        {
            filteredPermissionsByModule = permissionsByModule;
        }
        else
        {
            filteredPermissionsByModule = permissionsByModule
                .Where(kvp => kvp.Value.Any(p =>
                    p.Name.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                    p.Code.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                    p.Description?.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) == true
                ))
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.Where(p =>
                        p.Name.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                        p.Code.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) ||
                        p.Description?.Contains(permissionSearchKeyword, StringComparison.OrdinalIgnoreCase) == true
                    ).ToList()
                );
        }
        StateHasChanged();
    }

    private async Task SelectUser(User user)
    {
        selectedUser = user;
        await LoadUserRoles();
        await LoadUserDirectPermissions();
        await RefreshUserPermissions();
    }

    private async Task LoadUserRoles()
    {
        if (selectedUser == null) return;

        try
        {
            isLoadingRoles = true;
            StateHasChanged();

            var roles = await UserManagementService.GetUserRolesAsync(selectedUser.Id);
            userRoles = roles.Select(r => r.Id).ToHashSet();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingRoles = false;
            StateHasChanged();
        }
    }

    private async Task LoadUserDirectPermissions()
    {
        if (selectedUser == null) return;

        try
        {
            isLoadingPermissions = true;
            StateHasChanged();

            var permissions = await UserManagementService.GetUserDirectPermissionsAsync(selectedUser.Id);
            userDirectPermissions = permissions.Select(p => p.Id).ToHashSet();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户直接权限失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingPermissions = false;
            StateHasChanged();
        }
    }

    private async Task RefreshUserPermissions()
    {
        if (selectedUser == null) return;

        try
        {
            isLoadingAllPermissions = true;
            StateHasChanged();

            // 获取通过角色获得的权限
            roleBasedPermissions = new List<Permission>();
            foreach (var roleId in userRoles)
            {
                var rolePermissions = await UserManagementService.GetRolePermissionsAsync(roleId);
                roleBasedPermissions.AddRange(rolePermissions);
            }
            roleBasedPermissions = roleBasedPermissions.DistinctBy(p => p.Id).ToList();

            // 获取直接分配的权限
            directPermissions = await UserManagementService.GetUserDirectPermissionsAsync(selectedUser.Id);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"刷新用户权限失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingAllPermissions = false;
            StateHasChanged();
        }
    }

    private void ToggleRole(int roleId, bool isChecked)
    {
        if (isChecked)
        {
            userRoles.Add(roleId);
        }
        else
        {
            userRoles.Remove(roleId);
        }
    }

    private void TogglePermission(int permissionId, bool isChecked)
    {
        if (isChecked)
        {
            userDirectPermissions.Add(permissionId);
        }
        else
        {
            userDirectPermissions.Remove(permissionId);
        }
    }

    private async Task SaveUserRoles()
    {
        if (selectedUser == null) return;

        try
        {
            isSavingRoles = true;
            StateHasChanged();

            var result = await UserManagementService.UpdateUserRolesAsync(selectedUser.Id, userRoles.ToList());
            if (result.IsSuccess)
            {
                Snackbar.Add("用户角色保存成功", Severity.Success);
                await RefreshUserPermissions();
            }
            else
            {
                Snackbar.Add($"用户角色保存失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存用户角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingRoles = false;
            StateHasChanged();
        }
    }

    private async Task SaveUserPermissions()
    {
        if (selectedUser == null) return;

        try
        {
            isSavingPermissions = true;
            StateHasChanged();

            // 这里需要实现更新用户直接权限的方法
            // 由于当前接口设计，我们需要逐个添加/移除权限
            var currentPermissions = await UserManagementService.GetUserDirectPermissionsAsync(selectedUser.Id);
            var currentPermissionIds = currentPermissions.Select(p => p.Id).ToHashSet();

            // 添加新权限
            foreach (var permissionId in userDirectPermissions.Except(currentPermissionIds))
            {
                await UserManagementService.AssignUserPermissionAsync(selectedUser.Id, permissionId);
            }

            // 移除权限
            foreach (var permissionId in currentPermissionIds.Except(userDirectPermissions))
            {
                await UserManagementService.RemoveUserPermissionAsync(selectedUser.Id, permissionId);
            }

            Snackbar.Add("用户权限保存成功", Severity.Success);
            await RefreshUserPermissions();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存用户权限失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingPermissions = false;
            StateHasChanged();
        }
    }

    private string GetLevelText(int level)
    {
        return level switch
        {
            1 => "菜单级",
            2 => "页面级",
            3 => "功能级",
            4 => "数据级",
            _ => "未知"
        };
    }

    private Color GetLevelColor(int level)
    {
        return level switch
        {
            1 => Color.Primary,
            2 => Color.Success,
            3 => Color.Warning,
            4 => Color.Error,
            _ => Color.Default
        };
    }

    private string GetUserCardClass(User user)
    {
        return selectedUser?.Id == user.Id ? "mud-info mb-2" : "mb-2";
    }

    private string GetUserCardStyle(User user)
    {
        var baseStyle = "cursor: pointer;";
        if (selectedUser?.Id == user.Id)
        {
            baseStyle += " border: 2px solid var(--mud-palette-info);";
        }
        return baseStyle;
    }
} 