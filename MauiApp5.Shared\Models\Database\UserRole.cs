using SqlSugar;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 用户角色关联实体
    /// </summary>
    [SugarTable("UserRoles")]
    public class UserRole
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int UserId { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int RoleId { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime AssignedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 分配人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AssignedBy { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 过期时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 用户
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User User { get; set; } = null!;

        /// <summary>
        /// 角色
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Role Role { get; set; } = null!;
    }
} 