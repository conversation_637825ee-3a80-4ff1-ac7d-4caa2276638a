using Android.Content;
using MauiApp5.Shared.Services;

namespace MauiApp5.Platforms.Android
{
    /// <summary>
    /// Android平台认证状态存储实现 - 使用SharedPreferences
    /// </summary>
    public class AndroidAuthenticationStateStorage : IAuthenticationStateStorage
    {
        private const string PreferencesName = "MauiApp5_Auth";
        private readonly ISharedPreferences _preferences;

        public AndroidAuthenticationStateStorage()
        {
            var context = Platform.CurrentActivity?.ApplicationContext ?? Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.ApplicationContext;
            if (context == null)
            {
                throw new InvalidOperationException("无法获取Android应用程序上下文");
            }
            
            _preferences = context.GetSharedPreferences(PreferencesName, FileCreationMode.Private);
        }

        public bool IsAvailable => _preferences != null;

        public Task SaveAuthStateAsync(string key, string data)
        {
            try
            {
                var editor = _preferences.Edit();
                editor?.PutString(key, data);
                editor?.Apply();
                
                System.Diagnostics.Debug.WriteLine($"Android存储保存成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Android存储保存失败: {ex.Message}");
                throw;
            }
        }

        public Task<string?> GetAuthStateAsync(string key)
        {
            try
            {
                var data = _preferences.GetString(key, null);
                System.Diagnostics.Debug.WriteLine($"Android存储读取: {key} = {(data != null ? "有数据" : "无数据")}");
                return Task.FromResult(data);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Android存储读取失败: {ex.Message}");
                return Task.FromResult<string?>(null);
            }
        }

        public Task ClearAuthStateAsync(string key)
        {
            try
            {
                var editor = _preferences.Edit();
                editor?.Remove(key);
                editor?.Apply();
                
                System.Diagnostics.Debug.WriteLine($"Android存储清除成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Android存储清除失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }
    }
} 