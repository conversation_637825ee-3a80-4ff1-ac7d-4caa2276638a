﻿using Microsoft.Extensions.Logging;
using MauiApp5.Shared.Services;
using MauiApp5.Services;
using ZXing.Net.Maui;
using ZXing.Net.Maui.Controls;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Configuration;
using MauiApp5.Shared.Configuration;
using MudBlazor.Services;

namespace MauiApp5
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseBarcodeReader()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddMudServices();

            // 使用统一的配置管理
            builder.Configuration.AddInMemoryCollection(DatabaseConfig.GetConfigurationData());

            // 添加Authorization服务
            builder.Services.AddAuthorizationCore();

            // 注册FormFactor服务
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            // 根据不同平台注册不同的服务实现
#if ANDROID
            builder.Services.AddSingleton<MauiApp5.Shared.Services.INotificationService, Platforms.Android.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Android.QrCodeScannerService>();
            // 注册Android平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Android.AndroidAuthenticationStateStorage>();
#elif IOS
            builder.Services.AddSingleton<MauiApp5.Shared.Services.INotificationService, Platforms.iOS.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.iOS.QrCodeScannerService>();
            // 注册iOS平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.iOS.iOSAuthenticationStateStorage>();
#elif MACCATALYST
            builder.Services.AddSingleton<MauiApp5.Shared.Services.INotificationService, Platforms.MacCatalyst.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.MacCatalyst.QrCodeScannerService>();
            // 注册MacCatalyst平台的认证状态存储（创建MacCatalyst版本）
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.MacCatalyst.MacCatalystAuthenticationStateStorage>();
#elif WINDOWS
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 注册Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
#else
            builder.Services.AddSingleton<INotificationService, Platforms.Windows.NotificationService>();
            builder.Services.AddSingleton<IQrCodeScannerService, Platforms.Windows.QrCodeScannerService>();
            // 默认使用Windows平台的认证状态存储
            builder.Services.AddSingleton<IAuthenticationStateStorage, Platforms.Windows.WindowsAuthenticationStateStorage>();
#endif

            // 注册数据库上下文
            builder.Services.AddScoped<MauiApp5.Shared.Data.DatabaseContext>();

            // 根据配置选择认证服务实现（与Web项目完全一致）
            var useStoredProcedure = builder.Configuration.GetValue<bool>("AuthenticationSettings:UseStoredProcedure");
            if (useStoredProcedure)
            {
                builder.Services.AddScoped<IUserAuthenticationService, MauiApp5.Shared.Services.StoredProcedureAuthenticationService>();
            }
            else
            {
                builder.Services.AddScoped<IUserAuthenticationService, MauiApp5.Shared.Services.DatabaseAuthenticationService>();
            }

            // 注册用户管理服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IUserManagementService, MauiApp5.Shared.Services.UserManagementService>();

            // 注册菜单服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMenuService, MauiApp5.Shared.Services.MenuService>();

            // 注册设备管理服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IDepartmentService, MauiApp5.Shared.Services.DepartmentService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IEquipmentModelService, MauiApp5.Shared.Services.EquipmentModelService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.ILocationService, MauiApp5.Shared.Services.LocationService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IEquipmentService, MauiApp5.Shared.Services.EquipmentService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRepairOrderService, MauiApp5.Shared.Services.RepairOrderService>();

            // 注册权限服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRoleDepartmentPermissionService, MauiApp5.Shared.Services.RoleDepartmentPermissionService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRoleDepartmentAssignmentServiceV2, MauiApp5.Shared.Services.RoleDepartmentAssignmentServiceV2>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMaintenanceDepartmentPermissionService, MauiApp5.Shared.Services.MaintenanceDepartmentPermissionService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IPermissionValidationService, MauiApp5.Shared.Services.PermissionValidationService>();

            // 注册部门类型和工种类型服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IDepartmentTypeService, MauiApp5.Shared.Services.DepartmentTypeService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IJobTypeService, MauiApp5.Shared.Services.JobTypeService>();

            // 注册维修工作流服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMaintenanceDashboardService, MauiApp5.Shared.Services.MaintenanceDashboardService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRepairWorkflowService, MauiApp5.Shared.Services.RepairWorkflowService>();

            // 注册持久化认证状态提供器
            builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

#if DEBUG
    		builder.Services.AddBlazorWebViewDeveloperTools();
    		builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
