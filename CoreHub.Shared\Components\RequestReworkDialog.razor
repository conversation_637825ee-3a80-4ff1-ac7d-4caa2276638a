@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Utils
@inject IRepairOrderService RepairOrderService
@inject IRepairWorkflowService RepairWorkflowService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudText Typo="Typo.h6" Class="mb-4">要求重新维修</MudText>
            
            <MudAlert Severity="Severity.Warning" Class="mb-4">
                您即将要求对报修单 <strong>@OrderNumber</strong> 重新进行维修。请说明要求重修的原因。
            </MudAlert>

            <MudTextField @bind-Value="reworkReason"
                         Label="重修原因"
                         Lines="4"
                         Placeholder="请详细说明为什么需要重新维修..."
                         Required="true"
                         RequiredError="请填写重修原因"
                         MaxLength="500"
                         Counter="500"
                         Variant="Variant.Outlined" />
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Warning" 
                   Variant="Variant.Filled" 
                   OnClick="Submit"
                   Disabled="@(string.IsNullOrWhiteSpace(reworkReason) || isSubmitting)">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">提交中...</MudText>
            }
            else
            {
                <MudText>确认要求重修</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] public int RepairOrderId { get; set; }
    [Parameter] public string OrderNumber { get; set; } = string.Empty;

    private string reworkReason = string.Empty;
    private bool isSubmitting = false;

    private void Cancel() => MudDialog.Cancel();

    private async Task Submit()
    {
        if (string.IsNullOrWhiteSpace(reworkReason))
        {
            Snackbar.Add("请填写重修原因", Severity.Warning);
            return;
        }

        isSubmitting = true;
        try
        {
            // 更新状态为处理中
            var result = await RepairOrderService.UpdateRepairOrderStatusAsync(RepairOrderId, RepairOrderStatusHelper.InProgress);
            if (result.IsSuccess)
            {
                // 记录工作流历史
                await RepairWorkflowService.AddWorkflowHistoryAsync(
                    RepairOrderId,
                    1, // 这里应该获取当前用户ID，暂时使用1
                    "要求重新维修",
                    reworkReason,
                    RepairOrderStatusHelper.PendingConfirmation, // 从待确认
                    RepairOrderStatusHelper.InProgress // 到处理中
                );

                Snackbar.Add("要求重修提交成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"提交失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"提交失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
