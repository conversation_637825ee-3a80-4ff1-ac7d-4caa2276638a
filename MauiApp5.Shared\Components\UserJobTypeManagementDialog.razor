@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@inject IJobTypeService JobTypeService
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.People" Class="mr-2" />
                管理工种：@JobType?.Name
            </MudText>

            <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                <!-- 当前用户列表 -->
                <MudTabPanel Text="当前用户" Icon="@Icons.Material.Filled.People">
                    <MudStack Spacing="3">
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">已分配用户 (@currentUsers.Count 人)</MudText>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="@Icons.Material.Filled.Refresh"
                                     OnClick="LoadCurrentUsers">
                                刷新
                            </MudButton>
                        </MudStack>

                        @if (loadingCurrent)
                        {
                            <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
                        }
                        else if (!currentUsers.Any())
                        {
                            <MudAlert Severity="Severity.Info">该工种暂无分配用户</MudAlert>
                        }
                        else
                        {
                            <MudDataGrid T="UserJobTypeDetail" 
                                       Items="@currentUsers" 
                                       Dense="true"
                                       Hover="true"
                                       Striped="true"
                                       FixedHeader="true"
                                       Height="400px">
                                <Columns>
                                    <PropertyColumn Property="x => x.User.Username" Title="用户名" />
                                    <PropertyColumn Property="x => x.User.DisplayName" Title="显示名称" />
                                    <TemplateColumn Title="主要工种" Sortable="false">
                                        <CellTemplate>
                                            <MudChip Color="@(context.Item.UserJobType.IsPrimary ? Color.Primary : Color.Default)" 
                                                   Size="Size.Small">
                                                @(context.Item.UserJobType.IsPrimary ? "是" : "否")
                                            </MudChip>
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <TemplateColumn Title="熟练程度" Sortable="false">
                                        <CellTemplate>
                                            <MudRating SelectedValue="@context.Item.UserJobType.SkillLevel" 
                                                     MaxValue="5" 
                                                     ReadOnly="true" 
                                                     Size="Size.Small" />
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <PropertyColumn Property="x => x.UserJobType.AcquiredAt" Title="获得时间" Format="yyyy-MM-dd" />
                                    <TemplateColumn Title="操作" Sortable="false">
                                        <CellTemplate>
                                            <MudStack Row Spacing="1">
                                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                             Color="Color.Primary" 
                                                             Size="Size.Small"
                                                             OnClick="() => EditUserJobType(context.Item)"
                                                             Title="编辑" />
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                             Color="Color.Error" 
                                                             Size="Size.Small"
                                                             OnClick="() => RemoveUserFromJobType(context.Item)"
                                                             Title="移除" />
                                            </MudStack>
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>
                        }
                    </MudStack>
                </MudTabPanel>

                <!-- 添加用户 -->
                <MudTabPanel Text="添加用户" Icon="@Icons.Material.Filled.PersonAdd">
                    <MudStack Spacing="3">
                        <MudText Typo="Typo.h6">为工种添加用户</MudText>
                        
                        <MudTextField @bind-Value="userSearchText" 
                                    Placeholder="搜索用户名或显示名称..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="@Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnKeyUp="OnUserSearchKeyUp" />

                        @if (loadingAvailable)
                        {
                            <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
                        }
                        else if (!filteredAvailableUsers.Any())
                        {
                            <MudAlert Severity="Severity.Info">没有可分配的用户</MudAlert>
                        }
                        else
                        {
                            <MudDataGrid T="User" 
                                       Items="@filteredAvailableUsers" 
                                       Dense="true"
                                       Hover="true"
                                       Striped="true"
                                       FixedHeader="true"
                                       Height="400px"
                                       MultiSelection="true"
                                       @bind-SelectedItems="selectedUsers">
                                <Columns>
                                    <SelectColumn T="User" />
                                    <PropertyColumn Property="x => x.Username" Title="用户名" />
                                    <PropertyColumn Property="x => x.DisplayName" Title="显示名称" />
                                    <PropertyColumn Property="x => x.Email" Title="邮箱" />
                                    <PropertyColumn Property="x => x.Phone" Title="电话" />
                                    <TemplateColumn Title="操作" Sortable="false">
                                        <CellTemplate>
                                            <MudButton Variant="Variant.Filled" 
                                                     Color="Color.Primary" 
                                                     Size="Size.Small"
                                                     OnClick="() => AddUserToJobType(context.Item)">
                                                添加
                                            </MudButton>
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>

                            @if (selectedUsers.Any())
                            {
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mt-4">
                                    <MudText>已选择 @selectedUsers.Count 个用户</MudText>
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary"
                                             OnClick="BatchAddUsersToJobType">
                                        批量添加选中用户
                                    </MudButton>
                                </MudStack>
                            }
                        }
                    </MudStack>
                </MudTabPanel>
            </MudTabs>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public JobType? JobType { get; set; }

    // 当前用户相关
    private List<UserJobTypeDetail> currentUsers = new();
    private bool loadingCurrent = false;

    // 可用用户相关
    private List<User> availableUsers = new();
    private List<User> filteredAvailableUsers = new();
    private HashSet<User> selectedUsers = new();
    private bool loadingAvailable = false;
    private string userSearchText = string.Empty;

    // 用户工种详情类
    public class UserJobTypeDetail
    {
        public UserJobType UserJobType { get; set; } = null!;
        public User User { get; set; } = null!;
    }

    protected override async Task OnInitializedAsync()
    {
        if (JobType != null)
        {
            await LoadCurrentUsers();
            await LoadAvailableUsers();
        }
    }

    private async Task LoadCurrentUsers()
    {
        if (JobType == null) return;

        loadingCurrent = true;
        try
        {
            var userJobTypesWithDetails = await JobTypeService.GetJobTypeUsersWithDetailsAsync(JobType.Id);
            currentUsers = userJobTypesWithDetails.Select(x => new UserJobTypeDetail
            {
                UserJobType = x.UserJobType,
                User = x.User
            }).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载当前用户失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingCurrent = false;
        }
    }

    private async Task LoadAvailableUsers()
    {
        if (JobType == null) return;

        loadingAvailable = true;
        try
        {
            // 获取所有启用的用户
            var allUsers = await UserManagementService.GetAllUsersAsync();
            
            // 获取已分配该工种的用户ID
            var assignedUserIds = currentUsers.Select(cu => cu.User.Id).ToHashSet();
            
            // 过滤出未分配该工种的用户
            availableUsers = allUsers.Where(u => u.IsEnabled && !assignedUserIds.Contains(u.Id)).ToList();
            
            FilterAvailableUsers();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载可用用户失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingAvailable = false;
        }
    }

    private void FilterAvailableUsers()
    {
        var filtered = availableUsers.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(userSearchText))
        {
            filtered = filtered.Where(u =>
                u.Username.Contains(userSearchText, StringComparison.OrdinalIgnoreCase) ||
                u.DisplayName.Contains(userSearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        filteredAvailableUsers = filtered.ToList();
    }

    private void OnUserSearchKeyUp(KeyboardEventArgs e)
    {
        FilterAvailableUsers();
    }

    private async Task AddUserToJobType(User user)
    {
        if (JobType == null) return;

        try
        {
            var result = await JobTypeService.AssignJobTypeToUserAsync(user.Id, JobType.Id, false, 1, $"通过工种管理界面分配");
            if (result)
            {
                Snackbar.Add($"成功为用户 '{user.DisplayName}' 分配工种", Severity.Success);
                await LoadCurrentUsers();
                await LoadAvailableUsers();
            }
            else
            {
                Snackbar.Add("分配失败，可能该用户已拥有此工种", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"分配失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task BatchAddUsersToJobType()
    {
        if (JobType == null || !selectedUsers.Any()) return;

        try
        {
            int successCount = 0;
            foreach (var user in selectedUsers)
            {
                var result = await JobTypeService.AssignJobTypeToUserAsync(user.Id, JobType.Id, false, 1, $"通过工种管理界面批量分配");
                if (result) successCount++;
            }

            Snackbar.Add($"批量分配完成，成功分配 {successCount}/{selectedUsers.Count} 个用户", Severity.Success);
            selectedUsers.Clear();
            await LoadCurrentUsers();
            await LoadAvailableUsers();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"批量分配失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task RemoveUserFromJobType(UserJobTypeDetail detail)
    {
        if (JobType == null) return;

        try
        {
            var result = await JobTypeService.RemoveJobTypeFromUserAsync(detail.User.Id, JobType.Id);
            if (result)
            {
                Snackbar.Add($"成功移除用户 '{detail.User.DisplayName}' 的工种分配", Severity.Success);
                await LoadCurrentUsers();
                await LoadAvailableUsers();
            }
            else
            {
                Snackbar.Add("移除失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"移除失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task EditUserJobType(UserJobTypeDetail detail)
    {
        var parameters = new DialogParameters<UserJobTypeEditDialog>
        {
            { x => x.UserJobType, detail.UserJobType },
            { x => x.User, detail.User },
            { x => x.JobType, JobType }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };

        var dialog = await DialogService.ShowAsync<UserJobTypeEditDialog>($"编辑用户工种信息", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await LoadCurrentUsers();
        }
    }

    void Cancel() => MudDialog.Cancel();
}
