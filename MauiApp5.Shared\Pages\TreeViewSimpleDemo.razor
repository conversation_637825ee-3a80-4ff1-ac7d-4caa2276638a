@page "/treeview-simple"

<PageTitle>TreeView 简单演示</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h3" GutterBottom="true">MudBlazor TreeView 演示</MudText>

    <!-- 基本用法 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">1. 基本树形结构</MudText>
        <MudTreeView T="string">
            <MudTreeViewItem Value="@("文档")" Text="文档">
                <MudTreeViewItem Value="@("快速开始")" Text="快速开始" />
                <MudTreeViewItem Value="@("组件")" Text="组件">
                    <MudTreeViewItem Value="@("按钮")" Text="按钮" />
                    <MudTreeViewItem Value="@("输入框")" Text="输入框" />
                </MudTreeViewItem>
                <MudTreeViewItem Value="@("示例")" Text="示例" />
            </MudTreeViewItem>
        </MudTreeView>
    </MudPaper>

    <!-- 带图标的树 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">2. 带图标的树形结构</MudText>
        <MudTreeView T="string">
            <MudTreeViewItem Value="@("项目")" Text="我的项目" Icon="@Icons.Material.Filled.Folder" Expanded="true">
                <MudTreeViewItem Value="@("src")" Text="src" Icon="@Icons.Material.Filled.Folder">
                    <MudTreeViewItem Value="@("components")" Text="Components" Icon="@Icons.Material.Filled.Folder" />
                    <MudTreeViewItem Value="@("pages")" Text="Pages" Icon="@Icons.Material.Filled.Folder" />
                </MudTreeViewItem>
                <MudTreeViewItem Value="@("package.json")" Text="package.json" Icon="@Icons.Material.Filled.Description" />
            </MudTreeViewItem>
        </MudTreeView>
    </MudPaper>

    <!-- 选择事件处理 -->
    <MudPaper Class="pa-4 ma-2">
        <MudText Typo="Typo.h5" Class="mb-3">3. 选择事件处理</MudText>
        <MudTreeView T="string" SelectedValue="selectedValue" SelectedValueChanged="OnSelectionChanged">
            <MudTreeViewItem Value="@("开发工具")" Text="开发工具" Icon="@Icons.Material.Filled.Build">
                <MudTreeViewItem Value="@("IDE")" Text="IDE" Icon="@Icons.Material.Filled.Code" />
                <MudTreeViewItem Value="@("编辑器")" Text="编辑器" Icon="@Icons.Material.Filled.Edit" />
                <MudTreeViewItem Value="@("调试工具")" Text="调试工具" Icon="@Icons.Material.Filled.BugReport" />
            </MudTreeViewItem>
            <MudTreeViewItem Value="@("框架")" Text="框架" Icon="@Icons.Material.Filled.Web">
                <MudTreeViewItem Value="@("Blazor")" Text="Blazor" Icon="@Icons.Material.Filled.WebAsset" />
                <MudTreeViewItem Value="@("ASP.NET MVC")" Text="ASP.NET MVC" Icon="@Icons.Material.Filled.Architecture" />
                <MudTreeViewItem Value="@("Web API")" Text="Web API" Icon="@Icons.Material.Filled.Api" />
            </MudTreeViewItem>
        </MudTreeView>

        <MudDivider Class="my-3" />
        
        <MudAlert Severity="Severity.Info">
            @if (!string.IsNullOrEmpty(selectedValue))
            {
                <strong>当前选择：</strong> @selectedValue
            }
            else
            {
                <strong>请选择一个节点</strong>
            }
        </MudAlert>
    </MudPaper>
</MudContainer>

@code {
    private string selectedValue = "";

    private void OnSelectionChanged(string newValue)
    {
        selectedValue = newValue;
        StateHasChanged();
    }
} 