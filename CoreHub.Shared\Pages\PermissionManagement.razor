@page "/permissions"
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Data
@using Microsoft.Extensions.Logging
@using MauiApp5.Shared.Components
@using DatabaseMenuItem = MauiApp5.Shared.Models.Database.MenuItem
@inject IUserManagementService UserManagementService
@inject IMenuService MenuService
@inject ILogger<PermissionManagement> Logger
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>权限管理</PageTitle>

<PermissionView RequiredPermission="PermissionManagement.View">
    <ChildContent>
        <MudPaper Elevation="2" Class="pa-4">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mb-6">
                    <MudText Typo="Typo.h4" Color="Color.Primary">权限管理</MudText>
                    <MudSpacer />
                    <MudStack Row Spacing="3" AlignItems="AlignItems.Center">
                        <MudTextField @bind-Value="searchKeyword"
                                      @onkeypress="OnSearchKeyPress"
                                      Label="搜索权限"
                                      Placeholder="搜索权限代码、名称、模块或描述"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Search"
                                      Class="mud-width-full" />
                        <MudButton OnClick="SearchPermissions" 
                                   Color="Color.Primary" 
                                   Variant="Variant.Outlined"
                                   StartIcon="@Icons.Material.Filled.Search">
                            搜索
                        </MudButton>
                        <MudButton OnClick="LoadPermissions" 
                                   Color="Color.Default" 
                                   Variant="Variant.Outlined"
                                   StartIcon="@Icons.Material.Filled.Refresh">
                            刷新
                        </MudButton>
                        <PermissionView RequiredPermission="PermissionManagement.Create">
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="ShowCreateDialog">
                                新增权限
                            </MudButton>
                        </PermissionView>
                    </MudStack>
                </MudStack>

                @if (isLoading)
                {
                    <MudStack AlignItems="AlignItems.Center" Class="pa-10">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </MudStack>
                }
                else if (permissions?.Any() == true)
                {
                    <MudDataGrid Items="@GetFilteredPermissions()" 
                                 Hover="true" 
                                 Striped="true"
                                 Dense="true"
                                 Elevation="0">
                        <Columns>
                            <PropertyColumn Property="x => x.Code" Title="权限代码">
                                <CellTemplate>
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudText Typo="Typo.body2" Class="font-monospace">@context.Item.Code</MudText>
                                        @if (context.Item.IsSystem)
                                        {
                                            <MudChip T="string" Color="Color.Info" Size="Size.Small">系统</MudChip>
                                        }
                                    </MudStack>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.Name" Title="权限名称" />
                            
                            <PropertyColumn Property="x => x.Description" Title="描述" />
                            
                            <PropertyColumn Property="x => x.Module" Title="模块">
                                <CellTemplate>
                                    <MudChip T="string" Color="Color.Secondary" Size="Size.Small">@context.Item.Module</MudChip>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.Action" Title="操作">
                                <CellTemplate>
                                    <MudChip T="string" Color="Color.Primary" Size="Size.Small">@context.Item.Action</MudChip>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.Level" Title="级别">
                                <CellTemplate>
                                    @{
                                        var levelInfo = GetLevelInfo(context.Item.Level);
                                    }
                                    <MudChip T="string" Color="@levelInfo.Color" Size="Size.Small">@levelInfo.Text</MudChip>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.RouteUrl" Title="路由地址">
                                <CellTemplate>
                                    @if (!string.IsNullOrEmpty(context.Item.RouteUrl))
                                    {
                                        <MudText Typo="Typo.body2" Class="font-monospace">@context.Item.RouteUrl</MudText>
                                    }
                                    else
                                    {
                                        <MudText Color="Color.Default">-</MudText>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.SortOrder" Title="排序" />
                            
                            <PropertyColumn Property="x => x.IsEnabled" Title="状态">
                                <CellTemplate>
                                    @if (context.Item.IsEnabled)
                                    {
                                        <MudChip T="string" Color="Color.Success" Size="Size.Small">启用</MudChip>
                                    }
                                    else
                                    {
                                        <MudChip T="string" Color="Color.Error" Size="Size.Small">禁用</MudChip>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <TemplateColumn Title="操作" Sortable="false">
                                <CellTemplate>
                                    <MudStack Row Spacing="1">
                                        <PermissionView RequiredPermission="PermissionManagement.Edit">
                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                          Size="Size.Small"
                                                          Color="Color.Primary"
                                                          title="编辑"
                                                          OnClick="() => ShowEditDialog(context.Item)" />
                                        </PermissionView>
                                        
                                        @if (!context.Item.IsSystem)
                                        {
                                            <PermissionView RequiredPermission="PermissionManagement.Delete">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                              Size="Size.Small"
                                                              Color="Color.Error"
                                                              title="删除"
                                                              OnClick="() => DeletePermission(context.Item)" />
                                            </PermissionView>
                                        }
                                    </MudStack>
                                </CellTemplate>
                            </TemplateColumn>
                        </Columns>
                    </MudDataGrid>
                }
                else
                {
                    <MudStack AlignItems="AlignItems.Center" Class="py-15 px-5">
                        <MudIcon Icon="@Icons.Material.Filled.Security" 
                                 Size="Size.Large" 
                                 Color="Color.Default" 
                                 Class="mb-4"
                                 Style="font-size: 4rem;" />
                        <MudText Typo="Typo.h6" Color="Color.Default" Class="mb-4">
                            @if (!string.IsNullOrWhiteSpace(searchKeyword))
                            {
                                <text>没有找到匹配的权限数据</text>
                            }
                            else
                            {
                                <text>暂无权限数据</text>
                            }
                        </MudText>
                    </MudStack>
                }
            </MudPaper>
    </ChildContent>
    
    <NotAuthorized>
        <MudPaper Elevation="2" Class="pa-8 text-center mx-auto" Style="max-width: 600px;">
            <MudIcon Icon="@Icons.Material.Filled.Lock" 
                     Size="Size.Large" 
                     Color="Color.Error" 
                     Class="mb-4"
                     Style="font-size: 4rem;" />
            <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">权限不足</MudText>
            <MudText Typo="Typo.body1" Color="Color.Default">您没有权限访问权限管理页面</MudText>
        </MudPaper>
    </NotAuthorized>
</PermissionView>

<!-- 权限编辑对话框 -->
@if (showDialog)
{
    <MudOverlay @bind-Visible="showDialog" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6 ma-5" Style="width: 900px; max-width: 90vw; max-height: 90vh; overflow-y: auto;">
            <MudStack Row AlignItems="AlignItems.Center" Class="mb-6">
                <MudIcon Icon="@(isEditing ? Icons.Material.Filled.Edit : Icons.Material.Filled.Add)" Class="mr-3" />
                <MudText Typo="Typo.h5">@(isEditing ? "编辑权限" : "新增权限")</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="CloseDialog" />
            </MudStack>

            <EditForm Model="currentPermission" OnValidSubmit="HandlePermissionSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentPermission.Code"
                                      Label="权限代码"
                                      Required="true"
                                      RequiredError="请输入权限代码"
                                      Placeholder="如: UserManagement.View"
                                      HelperText="格式：模块名.操作名"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentPermission.Name"
                                      Label="权限名称"
                                      Required="true"
                                      RequiredError="请输入权限名称"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudTextField @bind-Value="currentPermission.Module"
                                      Label="模块名称"
                                      Required="true"
                                      RequiredError="请输入模块名称"
                                      Placeholder="如: UserManagement"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudTextField @bind-Value="currentPermission.Action"
                                      Label="操作名称"
                                      Required="true"
                                      RequiredError="请输入操作名称"
                                      Placeholder="如: View, Create, Edit"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudSelect T="int" @bind-Value="currentPermission.Level"
                                   Label="权限级别"
                                   Variant="Variant.Outlined">
                            <MudSelectItem T="int" Value="1">菜单级</MudSelectItem>
                            <MudSelectItem T="int" Value="2">页面级</MudSelectItem>
                            <MudSelectItem T="int" Value="3">功能级</MudSelectItem>
                            <MudSelectItem T="int" Value="4">数据级</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" sm="8">
                        <MudStack Row Spacing="2">
                            <MudSelect T="string" @bind-Value="currentPermission.RouteUrl"
                                       Label="路由地址"
                                       Variant="Variant.Outlined"
                                       Clearable="true"
                                       Class="flex-grow-1">
                                <MudSelectItem T="string" Value="@((string)null!)">-- 请选择路由地址 --</MudSelectItem>
                                @foreach (var menuItem in availableMenuItems)
                                {
                                    <MudSelectItem T="string" Value="@menuItem.RouteUrl">@menuItem.Name (@menuItem.RouteUrl)</MudSelectItem>
                                }
                            </MudSelect>
                            <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                          Color="Color.Default"
                                          title="刷新菜单列表"
                                          OnClick="RefreshMenuItems" />
                        </MudStack>
                        <MudText Typo="Typo.caption" Color="Color.Default">从菜单项中选择路由地址，或留空表示非页面权限</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" sm="4">
                        <MudNumericField T="int" @bind-Value="currentPermission.SortOrder"
                                         Label="排序号"
                                         Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentPermission.Description"
                                      Label="描述"
                                      Lines="3"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudCheckBox @bind-Value="currentPermission.IsEnabled"
                                     Label="启用权限"
                                     Color="Color.Primary" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudCheckBox @bind-Value="currentPermission.IsSystem"
                                     Label="系统权限"
                                     Color="Color.Primary" />
                    </MudItem>
                </MudGrid>
                
                <ValidationSummary />
                
                <MudStack Row Justify="Justify.FlexEnd" Spacing="3" Class="mt-6">
                    <MudButton OnClick="CloseDialog" Color="Color.Default">取消</MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                               Color="Color.Primary" 
                               Variant="Variant.Filled"
                               Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">保存中...</MudText>
                        }
                        else
                        {
                            <MudText>保存</MudText>
                        }
                    </MudButton>
                </MudStack>
            </EditForm>
        </MudPaper>
    </MudOverlay>
}

@code {
    private List<Permission>? permissions;
    private List<DatabaseMenuItem> availableMenuItems = new();
    private bool isLoading = true;
    private bool showDialog = false;
    private bool isEditing = false;
    private bool isSaving = false;
    private string searchKeyword = "";
    private Permission currentPermission = new Permission();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        await Task.WhenAll(
            LoadPermissions(),
            LoadMenuItems()
        );
    }

    private async Task LoadPermissions()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            permissions = await UserManagementService.GetAllPermissionsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载权限数据失败");
            Snackbar.Add("加载权限数据失败：" + ex.Message, Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadMenuItems()
    {
        try
        {
            var allMenuItems = await MenuService.GetAllMenusAsync();
            availableMenuItems = GetFlatMenuList(allMenuItems)
                .Where(m => !string.IsNullOrEmpty(m.RouteUrl) && m.MenuType == 1)
                .OrderBy(m => m.Name)
                .ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "加载菜单数据失败");
        }
    }

    private List<DatabaseMenuItem> GetFlatMenuList(List<DatabaseMenuItem> menus)
    {
        var result = new List<DatabaseMenuItem>();
        foreach (var menu in menus.OrderBy(m => m.SortOrder))
        {
            result.Add(menu);
            if (menu.Children?.Any() == true)
            {
                result.AddRange(GetFlatMenuList(menu.Children));
            }
        }
        return result;
    }

    private IEnumerable<Permission> GetSortedPermissions()
    {
        return permissions?.OrderBy(p => p.Module).ThenBy(p => p.SortOrder) ?? Enumerable.Empty<Permission>();
    }

    private IEnumerable<Permission> GetFilteredPermissions()
    {
        var allPermissions = GetSortedPermissions();
        
        if (string.IsNullOrWhiteSpace(searchKeyword))
        {
            return allPermissions;
        }

        return allPermissions.Where(p => 
            p.Code.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
            p.Name.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase) ||
            (!string.IsNullOrEmpty(p.Module) && p.Module.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
            (!string.IsNullOrEmpty(p.Action) && p.Action.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase)) ||
            (!string.IsNullOrEmpty(p.Description) && p.Description.Contains(searchKeyword, StringComparison.OrdinalIgnoreCase))
        );
    }

    private void SearchPermissions()
    {
        // 搜索是客户端过滤，不需要重新加载数据
        StateHasChanged();
        
        var filteredCount = GetFilteredPermissions().Count();
        var totalCount = permissions?.Count ?? 0;
        
        if (!string.IsNullOrWhiteSpace(searchKeyword))
        {
            Snackbar.Add($"搜索到 {filteredCount} 个权限（共 {totalCount} 个）", Severity.Info);
        }
        else
        {
            Snackbar.Add($"显示全部 {totalCount} 个权限", Severity.Info);
        }
    }

    private void OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            SearchPermissions();
        }
    }

    private (Color Color, string Text) GetLevelInfo(int level)
    {
        return level switch
        {
            1 => (Color.Error, "菜单级"),
            2 => (Color.Warning, "页面级"),
            3 => (Color.Info, "功能级"),
            4 => (Color.Success, "数据级"),
            _ => (Color.Default, level.ToString())
        };
    }

    private async Task RefreshMenuItems()
    {
        await LoadMenuItems();
        StateHasChanged();
    }

    private void ShowCreateDialog()
    {
        isEditing = false;
        currentPermission = new Permission
        {
            IsEnabled = true,
            Level = 2,
            SortOrder = (permissions?.Max(p => (int?)p.SortOrder) ?? 0) + 1
        };
        showDialog = true;
        StateHasChanged();
    }

    private void ShowEditDialog(Permission item)
    {
        isEditing = true;
        currentPermission = new Permission
        {
            Id = item.Id,
            Code = item.Code,
            Name = item.Name,
            Description = item.Description,
            Module = item.Module,
            Action = item.Action,
            Level = item.Level,
            SortOrder = item.SortOrder,
            RouteUrl = item.RouteUrl,
            IsEnabled = item.IsEnabled,
            IsSystem = item.IsSystem
        };
        showDialog = true;
        StateHasChanged();
    }

    private void CloseDialog()
    {
        showDialog = false;
        currentPermission = new Permission();
        isEditing = false;
        isSaving = false;
        StateHasChanged();
    }

    private async Task HandlePermissionSubmit()
    {
        await SavePermission();
    }

    private async Task SavePermission()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            (bool isSuccess, string errorMessage) result;

            if (isEditing)
            {
                result = await UserManagementService.UpdatePermissionAsync(currentPermission);
            }
            else
            {
                result = await UserManagementService.CreatePermissionAsync(currentPermission);
            }

            if (result.isSuccess)
            {
                Snackbar.Add(isEditing ? "权限更新成功！" : "权限创建成功！", Severity.Success);
                CloseDialog();
                await LoadPermissions();
            }
            else
            {
                Snackbar.Add("保存权限失败：" + result.errorMessage, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存权限失败");
            Snackbar.Add("保存权限失败：" + ex.Message, Severity.Error);
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeletePermission(Permission item)
    {
        var result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除权限 '{item.Name}' 吗？\n注意：删除权限可能影响相关用户的访问权限。",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                var deleteResult = await UserManagementService.DeletePermissionAsync(item.Id);

                if (deleteResult.IsSuccess)
                {
                    Snackbar.Add("权限删除成功！", Severity.Success);
                    await LoadPermissions();
                }
                else
                {
                    Snackbar.Add("删除权限失败：" + deleteResult.ErrorMessage, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "删除权限失败");
                Snackbar.Add("删除权限失败：" + ex.Message, Severity.Error);
            }
        }
    }
}