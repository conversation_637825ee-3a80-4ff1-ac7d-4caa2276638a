using MauiApp5.Web.Components;
using MauiApp5.Web.Services;
using MauiApp5.Shared.Services;
using Microsoft.AspNetCore.Components.Authorization;
using MauiApp5.Shared.Configuration;
using MudBlazor.Services;

namespace MauiApp5.Web
{
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddRazorComponents()
                .AddInteractiveServerComponents();
            builder.Services.AddMudServices();

            // Add CORS services
            builder.Services.AddCors(options =>
            {
                options.AddDefaultPolicy(
                    builder =>
                    {
                        builder.AllowAnyOrigin()
                               .AllowAnyMethod()
                               .AllowAnyHeader();
                    });
            });

            // 添加Authorization服务
            builder.Services.AddAuthorizationCore();

            // 注册FormFactor服务
            builder.Services.AddScoped<IFormFactor, FormFactor>();

            // 注册二维码扫描服务
            builder.Services.AddScoped<IQrCodeScannerService, WebQrCodeScannerService>();

            // 注册通知服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.INotificationService, WebNotificationService>();

            // 使用统一的数据库配置覆盖 appsettings.json
            builder.Configuration.AddInMemoryCollection(DatabaseConfig.GetConfigurationData());

            // 注册数据库上下文
            builder.Services.AddScoped<MauiApp5.Shared.Data.DatabaseContext>();

            // 根据统一配置选择认证服务实现
            if (DatabaseConfig.UseStoredProcedure)
            {
                builder.Services.AddScoped<IUserAuthenticationService, MauiApp5.Shared.Services.StoredProcedureAuthenticationService>();
            }
            else
            {
                builder.Services.AddScoped<IUserAuthenticationService, MauiApp5.Shared.Services.DatabaseAuthenticationService>();
            }

            // 注册Web平台的认证状态存储
            builder.Services.AddScoped<IAuthenticationStateStorage, WebAuthenticationStateStorage>();

            // 注册持久化认证状态提供器
            builder.Services.AddScoped<AuthenticationStateProvider, PersistentAuthenticationStateProvider>();

            // 注册用户管理服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IUserManagementService, MauiApp5.Shared.Services.UserManagementService>();

            // 注册菜单服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMenuService, MauiApp5.Shared.Services.MenuService>();

            // 注册设备管理服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IDepartmentService, MauiApp5.Shared.Services.DepartmentService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IEquipmentModelService, MauiApp5.Shared.Services.EquipmentModelService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.ILocationService, MauiApp5.Shared.Services.LocationService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IEquipmentService, MauiApp5.Shared.Services.EquipmentService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRepairOrderService, MauiApp5.Shared.Services.RepairOrderService>();

            // 注册权限服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRoleDepartmentPermissionService, MauiApp5.Shared.Services.RoleDepartmentPermissionService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRoleDepartmentAssignmentServiceV2, MauiApp5.Shared.Services.RoleDepartmentAssignmentServiceV2>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMaintenanceDepartmentPermissionService, MauiApp5.Shared.Services.MaintenanceDepartmentPermissionService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IPermissionValidationService, MauiApp5.Shared.Services.PermissionValidationService>();

            // 注册部门类型和工种类型服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IDepartmentTypeService, MauiApp5.Shared.Services.DepartmentTypeService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IJobTypeService, MauiApp5.Shared.Services.JobTypeService>();

            // 注册维修工作流服务
            builder.Services.AddScoped<MauiApp5.Shared.Services.IMaintenanceDashboardService, MauiApp5.Shared.Services.MaintenanceDashboardService>();
            builder.Services.AddScoped<MauiApp5.Shared.Services.IRepairWorkflowService, MauiApp5.Shared.Services.RepairWorkflowService>();

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error", createScopeForErrors: true);
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();

            app.UseStaticFiles();
            app.UseAntiforgery();
            app.UseCors();

            app.MapRazorComponents<App>()
                .AddInteractiveServerRenderMode()
                .AddAdditionalAssemblies(typeof(MauiApp5.Shared._Imports).Assembly);

            app.Run();
        }


    }
}
