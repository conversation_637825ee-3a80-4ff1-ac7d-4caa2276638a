@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.Large">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.Work" Class="mr-2" />
                工种分配：@User?.DisplayName
            </MudText>

            <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                <!-- 当前工种列表 -->
                <MudTabPanel Text="当前工种" Icon="@Icons.Material.Filled.Work">
                    <MudStack Spacing="3">
                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">已分配工种 (@currentJobTypes.Count 个)</MudText>
                            <MudButton Variant="Variant.Outlined" 
                                     StartIcon="@Icons.Material.Filled.Refresh"
                                     OnClick="LoadCurrentJobTypes">
                                刷新
                            </MudButton>
                        </MudStack>

                        @if (loadingCurrent)
                        {
                            <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
                        }
                        else if (!currentJobTypes.Any())
                        {
                            <MudAlert Severity="Severity.Info">该用户暂无分配工种</MudAlert>
                        }
                        else
                        {
                            <MudDataGrid T="UserJobTypeDetail" 
                                       Items="@currentJobTypes" 
                                       Dense="true"
                                       Hover="true"
                                       Striped="true"
                                       FixedHeader="true"
                                       Height="400px">
                                <Columns>
                                    <PropertyColumn Property="x => x.JobType.Category" Title="分类" />
                                    <PropertyColumn Property="x => x.JobType.Code" Title="工种编码" />
                                    <PropertyColumn Property="x => x.JobType.Name" Title="工种名称" />
                                    <TemplateColumn Title="主要工种" Sortable="false">
                                        <CellTemplate>
                                            <MudChip Color="@(context.Item.UserJobType.IsPrimary ? Color.Primary : Color.Default)" 
                                                   Size="Size.Small">
                                                @(context.Item.UserJobType.IsPrimary ? "是" : "否")
                                            </MudChip>
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <TemplateColumn Title="熟练程度" Sortable="false">
                                        <CellTemplate>
                                            <MudRating SelectedValue="@context.Item.UserJobType.SkillLevel" 
                                                     MaxValue="5" 
                                                     ReadOnly="true" 
                                                     Size="Size.Small" />
                                        </CellTemplate>
                                    </TemplateColumn>
                                    <PropertyColumn Property="x => x.UserJobType.AcquiredAt" Title="获得时间" Format="yyyy-MM-dd" />
                                    <TemplateColumn Title="操作" Sortable="false">
                                        <CellTemplate>
                                            <MudStack Row Spacing="1">
                                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                                             Color="Color.Primary" 
                                                             Size="Size.Small"
                                                             OnClick="() => EditUserJobType(context.Item)"
                                                             Title="编辑" />
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                                             Color="Color.Error" 
                                                             Size="Size.Small"
                                                             OnClick="() => RemoveJobTypeFromUser(context.Item)"
                                                             Title="移除" />
                                            </MudStack>
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>
                        }
                    </MudStack>
                </MudTabPanel>

                <!-- 添加工种 -->
                <MudTabPanel Text="添加工种" Icon="@Icons.Material.Filled.Add">
                    <MudStack Spacing="3">
                        <MudText Typo="Typo.h6">为用户添加工种</MudText>
                        
                        <MudStack Row Spacing="2" AlignItems="AlignItems.Center">
                            <MudTextField @bind-Value="jobTypeSearchText" 
                                        Placeholder="搜索工种名称或编码..." 
                                        Adornment="Adornment.Start" 
                                        AdornmentIcon="@Icons.Material.Filled.Search"
                                        Immediate="true"
                                        OnKeyUp="OnJobTypeSearchKeyUp"
                                        Style="flex: 1;" />
                            <MudSelect T="string" @bind-Value="selectedCategory" 
                                     Label="分类筛选" 
                                     Clearable="true"
                                     OnClearButtonClick="ClearCategoryFilter"
                                     Style="min-width: 150px;">
                                @foreach (var category in categories)
                                {
                                    <MudSelectItem T="string" Value="@category">@category</MudSelectItem>
                                }
                            </MudSelect>
                        </MudStack>

                        @if (loadingAvailable)
                        {
                            <MudProgressLinear Color="Color.Primary" Indeterminate="true" />
                        }
                        else if (!filteredAvailableJobTypes.Any())
                        {
                            <MudAlert Severity="Severity.Info">没有可分配的工种</MudAlert>
                        }
                        else
                        {
                            <MudDataGrid T="JobType" 
                                       Items="@filteredAvailableJobTypes" 
                                       Dense="true"
                                       Hover="true"
                                       Striped="true"
                                       FixedHeader="true"
                                       Height="400px"
                                       MultiSelection="true"
                                       @bind-SelectedItems="selectedJobTypes">
                                <Columns>
                                    <SelectColumn T="JobType" />
                                    <PropertyColumn Property="x => x.Category" Title="分类" />
                                    <PropertyColumn Property="x => x.Code" Title="工种编码" />
                                    <PropertyColumn Property="x => x.Name" Title="工种名称" />
                                    <PropertyColumn Property="x => x.Description" Title="描述" />
                                    <TemplateColumn Title="操作" Sortable="false">
                                        <CellTemplate>
                                            <MudButton Variant="Variant.Filled" 
                                                     Color="Color.Primary" 
                                                     Size="Size.Small"
                                                     OnClick="() => AddJobTypeToUser(context.Item)">
                                                添加
                                            </MudButton>
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>

                            @if (selectedJobTypes.Any())
                            {
                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Class="mt-4">
                                    <MudText>已选择 @selectedJobTypes.Count 个工种</MudText>
                                    <MudButton Variant="Variant.Filled" 
                                             Color="Color.Primary"
                                             OnClick="BatchAddJobTypesToUser">
                                        批量添加选中工种
                                    </MudButton>
                                </MudStack>
                            }
                        }
                    </MudStack>
                </MudTabPanel>
            </MudTabs>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public User? User { get; set; }

    // 当前工种相关
    private List<UserJobTypeDetail> currentJobTypes = new();
    private bool loadingCurrent = false;

    // 可用工种相关
    private List<JobType> availableJobTypes = new();
    private List<JobType> filteredAvailableJobTypes = new();
    private List<string> categories = new();
    private HashSet<JobType> selectedJobTypes = new();
    private bool loadingAvailable = false;
    private string jobTypeSearchText = string.Empty;
    private string selectedCategory = string.Empty;

    // 用户工种详情类
    public class UserJobTypeDetail
    {
        public UserJobType UserJobType { get; set; } = null!;
        public JobType JobType { get; set; } = null!;
    }

    protected override async Task OnInitializedAsync()
    {
        if (User != null)
        {
            await LoadCurrentJobTypes();
            await LoadAvailableJobTypes();
        }
    }

    private async Task LoadCurrentJobTypes()
    {
        if (User == null) return;

        loadingCurrent = true;
        try
        {
            var userJobTypesWithDetails = await JobTypeService.GetUserJobTypesWithDetailsAsync(User.Id);
            currentJobTypes = userJobTypesWithDetails.Select(x => new UserJobTypeDetail
            {
                UserJobType = x.UserJobType,
                JobType = x.JobType
            }).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载当前工种失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingCurrent = false;
        }
    }

    private async Task LoadAvailableJobTypes()
    {
        if (User == null) return;

        loadingAvailable = true;
        try
        {
            // 获取所有启用的工种
            var allJobTypes = await JobTypeService.GetAllJobTypesAsync();
            
            // 获取已分配的工种ID
            var assignedJobTypeIds = currentJobTypes.Select(cjt => cjt.JobType.Id).ToHashSet();
            
            // 过滤出未分配的工种
            availableJobTypes = allJobTypes.Where(jt => jt.IsEnabled && !assignedJobTypeIds.Contains(jt.Id)).ToList();
            
            // 获取分类列表
            categories = availableJobTypes.Select(jt => jt.Category).Distinct().OrderBy(c => c).ToList();
            
            FilterAvailableJobTypes();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载可用工种失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingAvailable = false;
        }
    }

    private void FilterAvailableJobTypes()
    {
        var filtered = availableJobTypes.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(jobTypeSearchText))
        {
            filtered = filtered.Where(jt =>
                jt.Name.Contains(jobTypeSearchText, StringComparison.OrdinalIgnoreCase) ||
                jt.Code.Contains(jobTypeSearchText, StringComparison.OrdinalIgnoreCase) ||
                jt.Category.Contains(jobTypeSearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        if (!string.IsNullOrWhiteSpace(selectedCategory))
        {
            filtered = filtered.Where(jt => jt.Category == selectedCategory);
        }

        filteredAvailableJobTypes = filtered.OrderBy(jt => jt.Category).ThenBy(jt => jt.Name).ToList();
    }

    private void OnJobTypeSearchKeyUp(KeyboardEventArgs e)
    {
        FilterAvailableJobTypes();
    }

    private void ClearCategoryFilter()
    {
        selectedCategory = string.Empty;
        FilterAvailableJobTypes();
    }

    private async Task AddJobTypeToUser(JobType jobType)
    {
        if (User == null) return;

        try
        {
            var result = await JobTypeService.AssignJobTypeToUserAsync(User.Id, jobType.Id, false, 1, $"通过用户管理界面分配");
            if (result)
            {
                Snackbar.Add($"成功为用户分配工种 '{jobType.Name}'", Severity.Success);
                await LoadCurrentJobTypes();
                await LoadAvailableJobTypes();
            }
            else
            {
                Snackbar.Add("分配失败，可能该用户已拥有此工种", Severity.Warning);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"分配失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task BatchAddJobTypesToUser()
    {
        if (User == null || !selectedJobTypes.Any()) return;

        try
        {
            int successCount = 0;
            foreach (var jobType in selectedJobTypes)
            {
                var result = await JobTypeService.AssignJobTypeToUserAsync(User.Id, jobType.Id, false, 1, $"通过用户管理界面批量分配");
                if (result) successCount++;
            }

            Snackbar.Add($"批量分配完成，成功分配 {successCount}/{selectedJobTypes.Count} 个工种", Severity.Success);
            selectedJobTypes.Clear();
            await LoadCurrentJobTypes();
            await LoadAvailableJobTypes();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"批量分配失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task RemoveJobTypeFromUser(UserJobTypeDetail detail)
    {
        if (User == null) return;

        try
        {
            var result = await JobTypeService.RemoveJobTypeFromUserAsync(User.Id, detail.JobType.Id);
            if (result)
            {
                Snackbar.Add($"成功移除工种 '{detail.JobType.Name}' 的分配", Severity.Success);
                await LoadCurrentJobTypes();
                await LoadAvailableJobTypes();
            }
            else
            {
                Snackbar.Add("移除失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"移除失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task EditUserJobType(UserJobTypeDetail detail)
    {
        // TODO: 实现编辑用户工种详情的对话框
        Snackbar.Add($"编辑工种 '{detail.JobType.Name}' 的详情功能待实现", Severity.Info);
    }

    void Cancel() => MudDialog.Cancel();
}
