using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 菜单项实体
    /// </summary>
    [SugarTable("MenuItems")]
    public class MenuItem
    {
        /// <summary>
        /// 菜单ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 菜单编码（唯一）
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "菜单编码不能为空")]
        [StringLength(100, ErrorMessage = "菜单编码长度不能超过100个字符")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 菜单名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "菜单名称不能为空")]
        [StringLength(100, ErrorMessage = "菜单名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 菜单描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "菜单描述长度不能超过500个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 路由地址
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "路由地址长度不能超过200个字符")]
        public string? RouteUrl { get; set; }

        /// <summary>
        /// 图标类名
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "图标类名长度不能超过100个字符")]
        public string? Icon { get; set; }

        /// <summary>
        /// 父级菜单ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ParentId { get; set; }

        /// <summary>
        /// 菜单级别（1=一级菜单,2=二级菜单,3=三级菜单）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统内置菜单
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 是否对所有用户可见（如首页）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// 关联的权限编码（用于权限控制）
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "权限编码长度不能超过100个字符")]
        public string? PermissionCode { get; set; }

        /// <summary>
        /// 菜单类型（1=菜单,2=分组标题）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int MenuType { get; set; } = 1;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 子菜单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<MenuItem> Children { get; set; } = new List<MenuItem>();

        /// <summary>
        /// 父菜单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public MenuItem? Parent { get; set; }
    }
} 