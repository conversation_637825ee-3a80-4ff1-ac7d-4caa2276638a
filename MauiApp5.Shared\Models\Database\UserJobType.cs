using SqlSugar;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 用户工种关联表（多对多关系）
    /// </summary>
    [SugarTable("UserJobTypes")]
    public class UserJobType
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int UserId { get; set; }

        /// <summary>
        /// 工种ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int JobTypeId { get; set; }

        /// <summary>
        /// 是否为主要工种
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// 熟练程度（1-5级）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SkillLevel { get; set; } = 1;

        /// <summary>
        /// 获得时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime AcquiredAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 分配人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AssignedBy { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remark { get; set; }

        // 导航属性
        // 移除导航属性以避免循环引用
        // 如需获取关联数据，请使用服务层方法
    }
}
