@page "/maintenance-dashboard"
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Components
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IMaintenanceDashboardService MaintenanceDashboardService
@inject IRepairOrderService RepairOrderService
@inject IRepairWorkflowService RepairWorkflowService
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject IRoleDepartmentAssignmentServiceV2 RoleDepartmentAssignmentService

@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>维修仪表板</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
    <!-- 页面标题 -->
    <MudText Typo="Typo.h4" Class="mb-4">
        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
        维修仪表板
    </MudText>

    @if (loading)
    {
        <MudProgressLinear Indeterminate="true" Class="mb-4" />
    }

    <!-- 统计卡片 -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4">
                <MudCardContent>
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h6" Color="Color.Warning">@dashboardData.TotalPending</MudText>
                            <MudText Typo="Typo.body2">待处理</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.PendingActions" Color="Color.Warning" Size="Size.Large" />
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4">
                <MudCardContent>
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h6" Color="Color.Info">@dashboardData.TotalInProgress</MudText>
                            <MudText Typo="Typo.body2">处理中</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.Engineering" Color="Color.Info" Size="Size.Large" />
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4">
                <MudCardContent>
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h6" Color="Color.Success">@dashboardData.TotalCompleted</MudText>
                            <MudText Typo="Typo.body2">已完成</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Large" />
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" sm="6" md="3">
            <MudCard Class="pa-4">
                <MudCardContent>
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <div>
                            <MudText Typo="Typo.h6" Color="Color.Error">@dashboardData.UrgentCount</MudText>
                            <MudText Typo="Typo.body2">紧急</MudText>
                        </div>
                        <MudIcon Icon="@Icons.Material.Filled.PriorityHigh" Color="Color.Error" Size="Size.Large" />
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 过滤器和操作栏 -->
    <MudCard Class="mb-4">
        <MudCardContent>
            <MudGrid AlignItems="Center.Center">
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect T="int?" @bind-Value="selectedStatus" Label="状态筛选" Clearable="true">
                        <MudSelectItem T="int?" Value="1">待处理</MudSelectItem>
                        <MudSelectItem T="int?" Value="2">处理中</MudSelectItem>
                        <MudSelectItem T="int?" Value="3">已完成</MudSelectItem>
                        <MudSelectItem T="int?" Value="4">已作废</MudSelectItem>
                        <MudSelectItem T="int?" Value="5">已关闭</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <MudSelect T="int?" @bind-Value="selectedUrgency" Label="紧急程度" Clearable="true">
                        <MudSelectItem T="int?" Value="1">紧急</MudSelectItem>
                        <MudSelectItem T="int?" Value="2">高</MudSelectItem>
                        <MudSelectItem T="int?" Value="3">中</MudSelectItem>
                        <MudSelectItem T="int?" Value="4">低</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <MudSelect T="int?" @bind-Value="selectedDepartment" Label="@($"维修部门 ({departments.Count})")" Clearable="true">
                        @foreach (var dept in departments)
                        {
                            <MudSelectItem T="int?" Value="dept.Id">@dept.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <MudStack Row Spacing="2">
                        <MudButton Variant="Variant.Filled" 
                                 Color="Color.Primary" 
                                 StartIcon="@Icons.Material.Filled.FilterList"
                                 OnClick="ApplyFilters">
                            筛选
                        </MudButton>
                        <MudButton Variant="Variant.Outlined" 
                                 StartIcon="@Icons.Material.Filled.Clear"
                                 OnClick="ClearFilters">
                            清除
                        </MudButton>
                    </MudStack>
                </MudItem>
            </MudGrid>

            <!-- 搜索框 -->
            <MudTextField @bind-Value="searchTerm" 
                        Label="搜索报修单" 
                        Placeholder="输入报修单号、设备名称或故障描述..."
                        Adornment="Adornment.End" 
                        AdornmentIcon="@Icons.Material.Filled.Search"
                        OnAdornmentClick="SearchRepairOrders"
                        OnKeyPress="OnSearchKeyPress"
                        Class="mt-3" />
        </MudCardContent>
    </MudCard>

    <!-- 报修单列表 -->
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">报修单列表</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudStack Row Spacing="2">
                    <MudButton Variant="Variant.Filled" 
                             Color="Color.Secondary" 
                             StartIcon="@Icons.Material.Filled.Assignment"
                             OnClick="OpenBatchAssignDialog"
                             Disabled="@(!selectedRepairOrders.Any())">
                        批量分配 (@selectedRepairOrders.Count)
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                             StartIcon="@Icons.Material.Filled.Refresh"
                             OnClick="RefreshData">
                        刷新
                    </MudButton>
                </MudStack>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudDataGrid T="RepairOrderDetailDto" 
                       Items="@filteredRepairOrders" 
                       Loading="@loading"
                       MultiSelection="true"
                       @bind-SelectedItems="selectedRepairOrders"
                       Hover="true" 
                       Striped="true"
                       Dense="true"
                       FixedHeader="true"
                       Height="600px">
                <Columns>
                    <SelectColumn T="RepairOrderDetailDto" />
                    <PropertyColumn Property="x => x.OrderNumber" Title="报修单号" />
                    <PropertyColumn Property="x => x.EquipmentName" Title="设备名称" />
                    <PropertyColumn Property="x => x.ReporterName" Title="报修人" />
                    <TemplateColumn Title="紧急程度" Sortable="false">
                        <CellTemplate>
                            <MudChip T="string" Color="@GetUrgencyColor(context.Item.UrgencyLevel)"
                                   Size="Size.Small">
                                @GetUrgencyName(context.Item.UrgencyLevel)
                            </MudChip>
                        </CellTemplate>
                    </TemplateColumn>
                    <TemplateColumn Title="状态" Sortable="false">
                        <CellTemplate>
                            <MudChip T="string" Color="@GetStatusColor(context.Item.Status)"
                                   Size="Size.Small">
                                @GetStatusName(context.Item.Status)
                            </MudChip>
                        </CellTemplate>
                    </TemplateColumn>
                    <PropertyColumn Property="x => x.AssignedToName" Title="分配给" />
                    <PropertyColumn Property="x => x.ReportedAt" Title="报修时间" Format="MM-dd HH:mm" />
                    <TemplateColumn Title="操作" Sortable="false" Filterable="false">
                        <CellTemplate>
                            <MudStack Row Spacing="1">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                             Color="Color.Info" 
                                             Size="Size.Small"
                                             OnClick="() => ViewRepairOrderDetail(context.Item)"
                                             Title="查看详情" />
                                @if (CanAssignRepairOrder(context.Item))
                                {
                                    <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                                 Color="Color.Primary" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenAssignDialog(context.Item)"
                                                 Title="分配" />
                                }
                                @if (CanProcessRepairOrder(context.Item))
                                {
                                    <MudIconButton Icon="@Icons.Material.Filled.PlayArrow" 
                                                 Color="Color.Success" 
                                                 Size="Size.Small"
                                                 OnClick="() => OpenWorkflowDialog(context.Item)"
                                                 Title="处理" />
                                }
                            </MudStack>
                        </CellTemplate>
                    </TemplateColumn>
                </Columns>
            </MudDataGrid>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private bool loading = true;
    private int currentUserId = 0;
    private MaintenanceDashboardData dashboardData = new();
    private List<RepairOrderDetailDto> allRepairOrders = new();
    private List<RepairOrderDetailDto> filteredRepairOrders = new();
    private HashSet<RepairOrderDetailDto> selectedRepairOrders = new();
    private List<Department> departments = new();

    // 过滤条件
    private int? selectedStatus;
    private int? selectedUrgency;
    private int? selectedDepartment;
    private string searchTerm = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadData();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            Console.WriteLine("[MaintenanceDashboard] 开始获取当前用户信息...");
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId))
                {
                    currentUserId = userId;
                    Console.WriteLine($"[MaintenanceDashboard] 获取到当前用户ID: {currentUserId}");
                }
                else
                {
                    Console.WriteLine("[MaintenanceDashboard] 无法解析用户ID");
                    Console.WriteLine($"[MaintenanceDashboard] 可用的Claims: {string.Join(", ", authState.User.Claims.Select(c => $"{c.Type}={c.Value}"))}");
                }
            }
            else
            {
                Console.WriteLine("[MaintenanceDashboard] 用户未认证");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[MaintenanceDashboard] 获取用户信息失败: {ex}");
            Snackbar.Add($"获取用户信息失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            Console.WriteLine($"[MaintenanceDashboard] 开始加载数据，当前用户ID: {currentUserId}");
            if (currentUserId == 0)
            {
                Console.WriteLine("[MaintenanceDashboard] 用户ID为0，跳过数据加载");
                return;
            }

            Console.WriteLine("[MaintenanceDashboard] 开始并行加载数据...");
            var dashboardTask = MaintenanceDashboardService.GetDashboardDataAsync(currentUserId);
            var repairOrdersTask = RepairOrderService.GetMaintenanceVisibleRepairOrdersAsync(currentUserId);
            var departmentsTask = LoadMaintenanceDepartmentsAsync(currentUserId);

            await Task.WhenAll(dashboardTask, repairOrdersTask, departmentsTask);

            dashboardData = await dashboardTask;
            allRepairOrders = await repairOrdersTask;
            departments = await departmentsTask;

            Console.WriteLine($"[MaintenanceDashboard] 数据加载完成:");
            Console.WriteLine($"[MaintenanceDashboard] - 仪表板数据: 待处理={dashboardData.TotalPending}, 处理中={dashboardData.TotalInProgress}, 已完成={dashboardData.TotalCompleted}");
            Console.WriteLine($"[MaintenanceDashboard] - 报修单数量: {allRepairOrders.Count}");
            Console.WriteLine($"[MaintenanceDashboard] - 维修部门数量: {departments.Count}");

            ApplyCurrentFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[MaintenanceDashboard] 加载数据失败: {ex}");
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task<List<Department>> LoadMaintenanceDepartmentsAsync(int userId)
    {
        try
        {
            // 获取维修类型的部门作为可选择的维修部门
            var allMaintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();

            // 获取用户可以访问的部门（基于角色部门分配）
            var userAccessibleDepartments = await RoleDepartmentAssignmentService.GetUserAccessibleDepartmentsAsync(userId);

            // 如果用户有部门访问权限，则只显示有权限的维修部门；否则显示所有维修部门
            List<Department> result;
            if (userAccessibleDepartments.Any())
            {
                // 只显示用户有权限访问的维修部门
                result = allMaintenanceDepartments
                    .Where(md => userAccessibleDepartments.Any(ud => ud.Id == md.Id))
                    .ToList();
            }
            else
            {
                // 如果用户没有特定的部门权限，显示所有维修部门
                result = allMaintenanceDepartments;
            }

            // 加载部门类型信息
            foreach (var dept in result)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            Console.WriteLine($"[MaintenanceDashboard] 用户 {userId} 可访问的维修部门数量: {result.Count}");
            foreach (var dept in result)
            {
                Console.WriteLine($"[MaintenanceDashboard] 维修部门: {dept.Name} (ID: {dept.Id})");
            }

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[MaintenanceDashboard] 加载维修部门失败: {ex}");
            Snackbar.Add($"加载维修部门失败: {ex.Message}", Severity.Error);
            return new List<Department>();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
        Snackbar.Add("数据已刷新", Severity.Success);
    }

    private void ApplyCurrentFilters()
    {
        var query = allRepairOrders.AsQueryable();

        if (selectedStatus.HasValue)
            query = query.Where(ro => ro.Status == selectedStatus.Value);

        if (selectedUrgency.HasValue)
            query = query.Where(ro => ro.UrgencyLevel == selectedUrgency.Value);

        if (selectedDepartment.HasValue)
            query = query.Where(ro => ro.MaintenanceDepartmentId == selectedDepartment.Value);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var searchTermLower = searchTerm.ToLower();
            query = query.Where(ro =>
                ro.OrderNumber.ToLower().Contains(searchTermLower) ||
                ro.EquipmentName.ToLower().Contains(searchTermLower) ||
                ro.FaultDescription.ToLower().Contains(searchTermLower) ||
                (ro.ReporterName != null && ro.ReporterName.ToLower().Contains(searchTermLower))
            );
        }

        filteredRepairOrders = query.ToList();
    }

    private async Task ApplyFilters()
    {
        ApplyCurrentFilters();
        StateHasChanged();
    }

    private async Task ClearFilters()
    {
        selectedStatus = null;
        selectedUrgency = null;
        selectedDepartment = null;
        searchTerm = string.Empty;
        ApplyCurrentFilters();
        StateHasChanged();
    }

    private async Task SearchRepairOrders()
    {
        ApplyCurrentFilters();
        StateHasChanged();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchRepairOrders();
        }
    }

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,    // 紧急
            2 => Color.Warning,  // 高
            3 => Color.Info,     // 中
            4 => Color.Default,  // 低
            _ => Color.Default
        };
    }

    private string GetUrgencyName(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };
    }

    private Color GetStatusColor(int status)
    {
        return MauiApp5.Shared.Utils.RepairOrderStatusHelper.GetStatusColor(status);
    }

    private string GetStatusName(int status)
    {
        return MauiApp5.Shared.Utils.RepairOrderStatusHelper.GetStatusName(status);
    }

    private bool CanAssignRepairOrder(RepairOrderDetailDto repairOrder)
    {
        // 待处理状态且未分配的报修单可以分配
        return repairOrder.Status == 1 && repairOrder.AssignedTo == null;
    }

    private bool CanProcessRepairOrder(RepairOrderDetailDto repairOrder)
    {
        // 分配给当前用户的报修单可以处理
        return repairOrder.AssignedTo == currentUserId;
    }

    private async Task ViewRepairOrderDetail(RepairOrderDetailDto repairOrder)
    {
        // 导航到报修单详情页面
        Navigation.NavigateTo($"/repair-order-management");
    }

    private async Task OpenAssignDialog(RepairOrderDetailDto repairOrder)
    {
        var parameters = new DialogParameters
        {
            ["RepairOrder"] = repairOrder,
            ["AssignedByUserId"] = currentUserId
        };

        var dialog = await DialogService.ShowAsync<AssignRepairOrderDialog>("分配报修单", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await RefreshData();
        }
    }

    private async Task OpenBatchAssignDialog()
    {
        if (!selectedRepairOrders.Any())
        {
            Snackbar.Add("请先选择要分配的报修单", Severity.Warning);
            return;
        }

        var parameters = new DialogParameters
        {
            ["SelectedRepairOrders"] = selectedRepairOrders.ToList(),
            ["AssignedByUserId"] = currentUserId
        };

        var dialog = await DialogService.ShowAsync<BatchAssignRepairOrderDialog>("批量分配报修单", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            selectedRepairOrders.Clear();
            await RefreshData();
        }
    }

    private async Task OpenWorkflowDialog(RepairOrderDetailDto repairOrder)
    {
        var parameters = new DialogParameters
        {
            ["RepairOrder"] = repairOrder,
            ["UserId"] = currentUserId
        };

        var dialog = await DialogService.ShowAsync<RepairWorkflowDialog>("维修工作流操作", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            await RefreshData();
        }
    }
}
