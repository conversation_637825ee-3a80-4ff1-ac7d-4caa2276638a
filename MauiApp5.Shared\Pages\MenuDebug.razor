@page "/menu-debug"
@using MauiApp5.Shared.Services
@using MauiApp5.Shared.Models.Database
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using DatabaseMenuItem = MauiApp5.Shared.Models.Database.MenuItem
@inject IMenuService MenuService
@inject IUserManagementService UserManagementService
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>菜单调试</PageTitle>

<div class="container">
    <h2>菜单调试信息</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>用户信息</h4>
                </div>
                <div class="card-body">
                    @if (authState != null)
                    {
                        <p><strong>是否认证:</strong> @authState.User.Identity?.IsAuthenticated</p>
                        <p><strong>用户名:</strong> @authState.User.Identity?.Name</p>
                        <p><strong>UserId Claim:</strong> @authState.User.FindFirst("UserId")?.Value</p>
                        <p><strong>DisplayName Claim:</strong> @authState.User.FindFirst("DisplayName")?.Value</p>
                        
                        <h5>所有Claims:</h5>
                        <ul>
                            @foreach (var claim in authState.User.Claims)
                            {
                                <li><strong>@claim.Type:</strong> @claim.Value</li>
                            }
                        </ul>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>数据库中的菜单</h4>
                </div>
                <div class="card-body">
                    @if (allMenus != null)
                    {
                        <p><strong>总菜单数:</strong> @allMenus.Count</p>
                        @foreach (var menu in allMenus)
                        {
                            <div class="border p-2 mb-2">
                                <p><strong>@menu.Name</strong> (@menu.Code)</p>
                                <p>路由: @menu.RouteUrl</p>
                                <p>权限: @menu.PermissionCode</p>
                                <p>公开: @menu.IsPublic</p>
                                <p>启用: @menu.IsEnabled</p>
                                <p>类型: @menu.MenuType</p>
                            </div>
                        }
                    }
                    else
                    {
                        <p>正在加载...</p>
                    }
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>用户菜单结果</h4>
                </div>
                <div class="card-body">
                    @if (userMenus != null)
                    {
                        <p><strong>用户可见菜单数:</strong> @userMenus.Count</p>
                        @foreach (var menu in userMenus)
                        {
                            <div class="border p-2 mb-2 bg-light">
                                <p><strong>@menu.Name</strong> (@menu.Code)</p>
                                <p>路由: @menu.RouteUrl</p>
                                <p>权限: @menu.PermissionCode</p>
                                <p>公开: @menu.IsPublic</p>
                            </div>
                        }
                    }
                    else
                    {
                        <p>正在加载...</p>
                    }
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4>用户权限</h4>
                </div>
                <div class="card-body">
                    @if (userPermissions != null)
                    {
                        <p><strong>权限数量:</strong> @userPermissions.Count</p>
                        @foreach (var permission in userPermissions)
                        {
                            <p>• @permission.Code - @permission.Name</p>
                        }
                    }
                    else
                    {
                        <p>正在加载...</p>
                    }
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-3">
        <button class="btn btn-primary" @onclick="RefreshData">刷新数据</button>
    </div>
</div>

@code {
    private AuthenticationState? authState;
    private List<DatabaseMenuItem>? allMenus;
    private List<DatabaseMenuItem>? userMenus;
    private List<Permission>? userPermissions;
    private int? currentUserId;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            authState = await AuthStateProvider.GetAuthenticationStateAsync();
            
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId")?.Value;
                if (int.TryParse(userIdClaim, out var userId))
                {
                    currentUserId = userId;
                    userMenus = await MenuService.GetUserMenusAsync(userId);
                    
                    // 尝试获取用户权限，如果失败就使用简化版本
                    try
                    {
                        userPermissions = await UserManagementService.GetUserAllPermissionsAsync(userId);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取用户权限失败: {ex.Message}");
                        // 使用简化版本 - 只获取所有权限来测试
                        userPermissions = await UserManagementService.GetAllPermissionsAsync();
                    }
                }
                else
                {
                    userMenus = await MenuService.GetUserMenusAsync();
                    userPermissions = new List<Permission>();
                }
            }
            else
            {
                userMenus = await MenuService.GetUserMenusAsync();
                userPermissions = new List<Permission>();
            }
            
            allMenus = await MenuService.GetAllMenusAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LoadData 异常: {ex.Message}");
        }
        
        StateHasChanged();
    }

    private async Task RefreshData()
    {
        await LoadData();
    }
} 