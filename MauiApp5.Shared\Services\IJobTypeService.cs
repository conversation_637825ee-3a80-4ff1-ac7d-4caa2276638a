using MauiApp5.Shared.Models.Database;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 工种类型服务接口
    /// </summary>
    public interface IJobTypeService
    {
        /// <summary>
        /// 获取所有启用的工种类型
        /// </summary>
        Task<List<JobType>> GetEnabledJobTypesAsync();

        /// <summary>
        /// 获取所有工种类型
        /// </summary>
        Task<List<JobType>> GetAllJobTypesAsync();

        /// <summary>
        /// 根据ID获取工种类型
        /// </summary>
        Task<JobType?> GetJobTypeByIdAsync(int id);

        /// <summary>
        /// 根据编码获取工种类型
        /// </summary>
        Task<JobType?> GetJobTypeByCodeAsync(string code);

        /// <summary>
        /// 根据分类获取工种类型
        /// </summary>
        Task<List<JobType>> GetJobTypesByCategoryAsync(string category);

        /// <summary>
        /// 获取维修类工种
        /// </summary>
        Task<List<JobType>> GetMaintenanceJobTypesAsync();

        /// <summary>
        /// 获取生产类工种
        /// </summary>
        Task<List<JobType>> GetProductionJobTypesAsync();

        /// <summary>
        /// 创建工种类型
        /// </summary>
        Task<bool> CreateJobTypeAsync(JobType jobType);

        /// <summary>
        /// 更新工种类型
        /// </summary>
        Task<bool> UpdateJobTypeAsync(JobType jobType);

        /// <summary>
        /// 删除工种类型
        /// </summary>
        Task<bool> DeleteJobTypeAsync(int id);

        /// <summary>
        /// 检查工种类型编码是否存在
        /// </summary>
        Task<bool> ExistsByCodeAsync(string code, int? excludeId = null);

        /// <summary>
        /// 获取指定部门的维修工种用户
        /// </summary>
        Task<List<User>> GetMaintenanceUsersByDepartmentAsync(int departmentId);

        /// <summary>
        /// 检查用户是否为维修工种
        /// </summary>
        Task<bool> IsMaintenanceJobAsync(int userId);

        /// <summary>
        /// 根据工种获取用户列表
        /// </summary>
        Task<List<User>> GetUsersByJobTypeAsync(string jobTypeCode);

        // 用户工种多对多关系管理
        /// <summary>
        /// 为用户分配工种
        /// </summary>
        Task<bool> AssignJobTypeToUserAsync(int userId, int jobTypeId, bool isPrimary = false, int skillLevel = 1, string? remark = null);

        /// <summary>
        /// 移除用户的工种
        /// </summary>
        Task<bool> RemoveJobTypeFromUserAsync(int userId, int jobTypeId);

        /// <summary>
        /// 获取用户的所有工种
        /// </summary>
        Task<List<UserJobType>> GetUserJobTypesAsync(int userId);

        /// <summary>
        /// 获取工种的所有用户
        /// </summary>
        Task<List<UserJobType>> GetJobTypeUsersAsync(int jobTypeId);

        /// <summary>
        /// 更新用户工种关系
        /// </summary>
        Task<bool> UpdateUserJobTypeAsync(UserJobType userJobType);

        /// <summary>
        /// 批量分配用户工种
        /// </summary>
        Task<bool> BatchAssignJobTypesToUserAsync(int userId, List<int> jobTypeIds, int primaryJobTypeId);

        /// <summary>
        /// 获取工种的用户数量
        /// </summary>
        Task<int> GetJobTypeUserCountAsync(int jobTypeId);

        /// <summary>
        /// 检查用户是否拥有指定工种
        /// </summary>
        Task<bool> UserHasJobTypeAsync(int userId, int jobTypeId);

        /// <summary>
        /// 获取用户工种的详细信息（包含工种名称等）
        /// </summary>
        Task<List<(UserJobType UserJobType, JobType JobType)>> GetUserJobTypesWithDetailsAsync(int userId);

        /// <summary>
        /// 获取工种用户的详细信息（包含用户名称等）
        /// </summary>
        Task<List<(UserJobType UserJobType, User User)>> GetJobTypeUsersWithDetailsAsync(int jobTypeId);
    }
}
