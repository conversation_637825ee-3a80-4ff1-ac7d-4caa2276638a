@page "/debug-auth"
@inject AuthenticationStateProvider AuthStateProvider
@inject IUserAuthenticationService UserAuthService

<PageTitle>认证调试</PageTitle>

<div style="padding: 20px;">
    <h1>认证状态调试</h1>
    
    <AuthorizeView>
        <Authorized>
            <div style="background-color: #f6ffed; border: 1px solid #b7eb8f; padding: 16px; margin: 16px 0; border-radius: 6px;">
                <h3 style="color: #52c41a;">✅ 用户已认证</h3>
                
                <h4>用户信息:</h4>
                <ul>
                    <li><strong>用户名:</strong> @context.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value</li>
                    <li><strong>显示名称:</strong> @context.User.FindFirst("DisplayName")?.Value</li>
                    <li><strong>角色:</strong> @context.User.FindFirst("Role")?.Value</li>
                    <li><strong>邮箱:</strong> @context.User.FindFirst(System.Security.Claims.ClaimTypes.Email)?.Value</li>
                </ul>
                
                <h4>所有声明 (Claims):</h4>
                <div style="background-color: #fafafa; padding: 12px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    @foreach (var claim in context.User.Claims)
                    {
                        <div>@claim.Type: @claim.Value</div>
                    }
                </div>
                
                <h4>权限列表:</h4>
                @{
                    var permissions = context.User.Claims
                        .Where(c => c.Type == "Permission")
                        .Select(c => c.Value)
                        .ToList();
                }
                
                @if (permissions.Any())
                {
                    <div style="background-color: #e6f7ff; padding: 12px; border-radius: 4px;">
                        @foreach (var permission in permissions)
                        {
                            <span style="display: inline-block; background-color: #1890ff; color: white; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 12px;">
                                @permission
                            </span>
                        }
                    </div>
                }
                else
                {
                    <div style="background-color: #fff2e8; border: 1px solid #ffbb96; padding: 12px; border-radius: 4px; color: #d4380d;">
                        ⚠️ 用户没有任何权限
                    </div>
                }
                
                <h4>权限检查:</h4>
                <div style="margin: 12px 0;">
                    <div>UserManagement.View: 
                        @if (permissions.Contains("UserManagement.View"))
                        {
                            <span style="color: green; font-weight: bold;">✅ 有权限</span>
                        }
                        else
                        {
                            <span style="color: red; font-weight: bold;">❌ 无权限</span>
                        }
                    </div>
                </div>
            </div>
        </Authorized>
        <NotAuthorized>
            <div style="background-color: #fff2f0; border: 1px solid #ffccc7; padding: 16px; margin: 16px 0; border-radius: 6px;">
                <h3 style="color: #ff4d4f;">❌ 用户未认证</h3>
                <p>请先登录系统</p>
            </div>
        </NotAuthorized>
    </AuthorizeView>
    
    <div style="margin: 20px 0;">
        <button @onclick="RefreshAuthState" class="btn btn-primary">刷新认证状态</button>
        <button @onclick="TestUserService" class="btn btn-secondary" style="margin-left: 10px;">测试用户服务</button>
    </div>
    
    @if (!string.IsNullOrEmpty(debugMessage))
    {
        <div style="background-color: #f0f0f0; padding: 12px; border-radius: 4px; margin: 12px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap;">
            @debugMessage
        </div>
    }
</div>

@code {
    private string debugMessage = "";

    private async Task RefreshAuthState()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            debugMessage = $"认证状态刷新完成\n";
            debugMessage += $"IsAuthenticated: {authState.User.Identity?.IsAuthenticated}\n";
            debugMessage += $"AuthenticationType: {authState.User.Identity?.AuthenticationType}\n";
            debugMessage += $"Claims Count: {authState.User.Claims.Count()}\n";
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            debugMessage = $"刷新认证状态失败: {ex.Message}";
            StateHasChanged();
        }
    }

    private void TestUserService()
    {
        try
        {
            var allUsers = UserAuthService.GetAllUsers();
            debugMessage = $"用户服务测试完成\n";
            debugMessage += $"获取到 {allUsers.Count} 个用户\n";
            
            foreach (var user in allUsers.Take(3))
            {
                debugMessage += $"用户: {user.Username}, 权限数: {user.Permissions.Count}\n";
            }
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            debugMessage = $"用户服务测试失败: {ex.Message}\n{ex.StackTrace}";
            StateHasChanged();
        }
    }
} 