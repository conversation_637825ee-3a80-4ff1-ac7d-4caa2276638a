@page "/test-user-department"
@using MauiApp5.Shared.Models.Database
@using MauiApp5.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MudBlazor
@inject IUserManagementService UserManagementService
@inject IEquipmentService EquipmentService
@inject IDepartmentService DepartmentService
@inject IRoleDepartmentPermissionService RoleDepartmentPermissionService

@inject IDepartmentTypeService DepartmentTypeService
@inject IJobTypeService JobTypeService
@inject AuthenticationStateProvider AuthStateProvider
@inject ISnackbar Snackbar

<PageTitle>用户部门测试</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
    <MudPaper Class="pa-6">
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h4" Class="mb-6">
                    <MudIcon Icon="@Icons.Material.Filled.Science" Class="mr-2" />
                    用户部门功能测试
                </MudText>
            </MudItem>

            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">当前用户信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (currentUser != null)
                        {
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudText><strong>用户ID:</strong> @currentUser.Id</MudText>
                                    <MudText><strong>用户名:</strong> @currentUser.Username</MudText>
                                    <MudText><strong>显示名:</strong> @currentUser.DisplayName</MudText>
                                    <MudText><strong>部门ID:</strong> @(currentUser.DepartmentId?.ToString() ?? "未分配")</MudText>
                                    <MudText><strong>部门名称:</strong> @(currentUser.Department?.Name ?? "未分配")</MudText>
                                    @if (currentUser.Department?.DepartmentType != null)
                                    {
                                        <MudText><strong>部门类型:</strong> @currentUser.Department.DepartmentType.Name</MudText>
                                    }
                                    @if (userJobTypes.Any())
                                    {
                                        <MudText><strong>工种:</strong></MudText>
                                        @foreach (var ujt in userJobTypes.Take(3))
                                        {
                                            <MudChip T="string" Size="Size.Small" Color="@(ujt.UserJobType.IsPrimary ? Color.Primary : Color.Default)">
                                                @ujt.JobType.Name
                                                @if (ujt.UserJobType.IsPrimary) { <text> (主要)</text> }
                                            </MudChip>
                                        }
                                        @if (userJobTypes.Count > 3)
                                        {
                                            <MudText Typo="Typo.caption">等 @userJobTypes.Count 个工种</MudText>
                                        }
                                    }
                                </MudItem>
                            </MudGrid>
                        }
                        else
                        {
                            <MudText Color="Color.Warning">正在加载用户信息...</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">用户权限测试</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (userPermissions.Any())
                        {
                            <MudGrid>
                                <MudItem xs="12" md="4">
                                    <MudText Typo="Typo.h6" Color="Color.Primary">可报修部门</MudText>
                                    @foreach (var dept in reportableDepartments)
                                    {
                                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Class="ma-1">
                                            @dept.Name
                                        </MudChip>
                                    }
                                    @if (!reportableDepartments.Any())
                                    {
                                        <MudText Color="Color.Warning">无权限</MudText>
                                    }
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudText Typo="Typo.h6" Color="Color.Primary">可接收报修部门</MudText>
                                    @foreach (var dept in receivableDepartments)
                                    {
                                        <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ma-1">
                                            @dept.Name
                                        </MudChip>
                                    }
                                    @if (!receivableDepartments.Any())
                                    {
                                        <MudText Color="Color.Warning">无权限</MudText>
                                    }
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudText Typo="Typo.h6" Color="Color.Primary">可维修部门</MudText>
                                    @foreach (var dept in maintainableDepartments)
                                    {
                                        <MudChip T="string" Color="Color.Secondary" Size="Size.Small" Class="ma-1">
                                            @dept.Name
                                        </MudChip>
                                    }
                                    @if (!maintainableDepartments.Any())
                                    {
                                        <MudText Color="Color.Warning">无权限</MudText>
                                    }
                                </MudItem>
                            </MudGrid>
                        }
                        else
                        {
                            <MudText Color="Color.Warning">正在加载权限信息...</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">用户可报修设备列表</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (userEquipment.Any())
                        {
                            <MudDataGrid Items="@userEquipment" Filterable="true" SortMode="SortMode.Multiple" Groupable="false">
                                <Columns>
                                    <PropertyColumn Property="x => x.Code" Title="设备编码" />
                                    <PropertyColumn Property="x => x.Name" Title="设备名称" />
                                    <PropertyColumn Property="x => x.DepartmentName" Title="所属部门" />
                                    <PropertyColumn Property="x => x.LocationName" Title="位置" />
                                    <PropertyColumn Property="x => x.StatusName" Title="状态" />
                                </Columns>
                            </MudDataGrid>
                        }
                        else
                        {
                            <MudText Color="Color.Info">
                                @if (!reportableDepartments.Any())
                                {
                                    <span>您没有设备报修权限。</span>
                                }
                                else
                                {
                                    <span>您有权限的部门暂无设备或正在加载中...</span>
                                }
                            </MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">维修人员信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (isMaintenancePersonnel)
                        {
                            <MudAlert Severity="Severity.Success" Class="mb-4">
                                <MudText><strong>您是维修人员</strong></MudText>
                                <MudText>部门：@currentUser?.Department?.Name</MudText>
                                <MudText>工种：@string.Join(", ", userJobTypes.Where(ujt => ujt.JobType.Category == MauiApp5.Shared.Models.Database.JobCategories.Maintenance).Select(ujt => ujt.JobType.Name))</MudText>
                                <MudText>状态：可接单</MudText>
                            </MudAlert>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                <MudText>您不是维修人员</MudText>
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">所有部门列表</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (allDepartments.Any())
                        {
                            <MudDataGrid Items="@allDepartments" Filterable="true" SortMode="SortMode.Multiple" Groupable="false">
                                <Columns>
                                    <PropertyColumn Property="x => x.Id" Title="ID" />
                                    <PropertyColumn Property="x => x.Code" Title="部门编码" />
                                    <PropertyColumn Property="x => x.Name" Title="部门名称" />
                                    <PropertyColumn Property="x => x.Description" Title="描述" />
                                    <TemplateColumn Title="是否当前用户部门">
                                        <CellTemplate>
                                            @if (context.Item.Id == currentUser?.DepartmentId)
                                            {
                                                <MudChip Color="Color.Success" Size="Size.Small">是</MudChip>
                                            }
                                            else
                                            {
                                                <MudChip Color="Color.Default" Size="Size.Small">否</MudChip>
                                            }
                                        </CellTemplate>
                                    </TemplateColumn>
                                </Columns>
                            </MudDataGrid>
                        }
                        else
                        {
                            <MudText Color="Color.Info">正在加载部门信息...</MudText>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudStack Row Justify="Justify.Center" Spacing="4" Class="mt-6">
                    <MudButton Variant="Variant.Outlined" StartIcon="@Icons.Material.Filled.Refresh"
                        OnClick="RefreshData">
                        刷新数据
                    </MudButton>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" 
                        StartIcon="@Icons.Material.Filled.Build" Href="/create-repair-order">
                        创建报修单
                    </MudButton>
                </MudStack>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private User? currentUser = null;
    private List<EquipmentDetailDto> userEquipment = new();
    private List<Department> allDepartments = new();
    private List<Department> reportableDepartments = new();
    private List<Department> receivableDepartments = new();
    private List<Department> maintainableDepartments = new();
    private List<RoleDepartmentPermissionType> userPermissions = new();
    private bool isMaintenancePersonnel = false;
    private List<(UserJobType UserJobType, JobType JobType)> userJobTypes = new();
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
        await LoadData();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst("UserId") ?? authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    currentUserId = userId;
                    currentUser = await UserManagementService.GetUserByIdAsync(userId);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"获取当前用户信息失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadData()
    {
        try
        {
            // 加载所有部门
            allDepartments = await DepartmentService.GetEnabledDepartmentsAsync();

            // 加载用户权限相关的部门
            reportableDepartments = await RoleDepartmentPermissionService.GetUserReportableDepartmentsAsync(currentUserId);
            receivableDepartments = await RoleDepartmentPermissionService.GetUserReceivableDepartmentsAsync(currentUserId);
            maintainableDepartments = await RoleDepartmentPermissionService.GetUserMaintainableDepartmentsAsync(currentUserId);

            // 收集所有权限类型
            userPermissions.Clear();
            if (reportableDepartments.Any()) userPermissions.Add(RoleDepartmentPermissionType.CanReportEquipment);
            if (receivableDepartments.Any()) userPermissions.Add(RoleDepartmentPermissionType.CanReceiveRepair);
            if (maintainableDepartments.Any()) userPermissions.Add(RoleDepartmentPermissionType.CanMaintainEquipment);

            // 加载设备（只显示用户有权限报修的部门设备）
            var allEquipment = await EquipmentService.GetEquipmentDetailsAsync();
            var reportableDepartmentIds = reportableDepartments.Select(d => d.Id).ToList();
            if (reportableDepartmentIds.Any())
            {
                userEquipment = allEquipment.Where(e => reportableDepartmentIds.Contains(e.DepartmentId)).ToList();
            }

            // 加载用户的部门类型和工种信息
            if (currentUser?.DepartmentId.HasValue == true)
            {
                currentUser.Department = await DepartmentService.GetDepartmentByIdAsync(currentUser.DepartmentId.Value);
                if (currentUser.Department?.DepartmentTypeId.HasValue == true)
                {
                    currentUser.Department.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(currentUser.Department.DepartmentTypeId.Value);
                }
            }

            // 加载用户工种信息（多对多关系）
            if (currentUser != null)
            {
                userJobTypes = await JobTypeService.GetUserJobTypesWithDetailsAsync(currentUser.Id);
            }

            // 检查用户是否是维修人员（通过工种判断）
            var maintenanceJobTypes = await JobTypeService.GetJobTypesByCategoryAsync(MauiApp5.Shared.Models.Database.JobCategories.Maintenance);
            var userJobTypeIds = userJobTypes.Select(ujt => ujt.UserJobType.JobTypeId).ToList();
            isMaintenancePersonnel = maintenanceJobTypes.Any(mjt => userJobTypeIds.Contains(mjt.Id));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task RefreshData()
    {
        await LoadCurrentUser();
        await LoadData();
        StateHasChanged();
        Snackbar.Add("数据已刷新", Severity.Success);
    }
}
