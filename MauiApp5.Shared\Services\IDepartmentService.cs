using MauiApp5.Shared.Models.Database;

namespace MauiApp5.Shared.Services
{
    /// <summary>
    /// 部门服务接口
    /// </summary>
    public interface IDepartmentService
    {
        /// <summary>
        /// 获取所有部门
        /// </summary>
        Task<List<Department>> GetAllDepartmentsAsync();

        /// <summary>
        /// 根据ID获取部门
        /// </summary>
        Task<Department?> GetDepartmentByIdAsync(int id);

        /// <summary>
        /// 根据编码获取部门
        /// </summary>
        Task<Department?> GetDepartmentByCodeAsync(string code);

        /// <summary>
        /// 创建部门
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CreateDepartmentAsync(Department department);

        /// <summary>
        /// 更新部门
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateDepartmentAsync(Department department);

        /// <summary>
        /// 删除部门
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> DeleteDepartmentAsync(int id);

        /// <summary>
        /// 切换部门状态
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ToggleStatusAsync(int id);

        /// <summary>
        /// 获取启用的部门列表
        /// </summary>
        Task<List<Department>> GetEnabledDepartmentsAsync();

        /// <summary>
        /// 获取部门树形结构
        /// </summary>
        Task<List<Department>> GetDepartmentTreeAsync();

        /// <summary>
        /// 检查部门编码是否存在
        /// </summary>
        Task<bool> IsCodeExistsAsync(string code, int? excludeId = null);

        /// <summary>
        /// 检查部门名称是否存在
        /// </summary>
        Task<bool> IsNameExistsAsync(string name, int? excludeId = null);
    }
}
