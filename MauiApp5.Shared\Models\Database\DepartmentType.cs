using SqlSugar;

namespace MauiApp5.Shared.Models.Database
{
    /// <summary>
    /// 部门类型
    /// </summary>
    [SugarTable("DepartmentTypes")]
    public class DepartmentType
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 部门类型编码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 部门类型名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 导航属性：该类型下的部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<Department> Departments { get; set; } = new();
    }

    /// <summary>
    /// 部门类型枚举
    /// </summary>
    public enum DepartmentTypeEnum
    {
        /// <summary>
        /// 生产车间
        /// </summary>
        Production = 1,

        /// <summary>
        /// 维修部门
        /// </summary>
        Maintenance = 2,

        /// <summary>
        /// 管理部门
        /// </summary>
        Management = 3,

        /// <summary>
        /// 支持部门
        /// </summary>
        Support = 4
    }

    /// <summary>
    /// 部门类型常量
    /// </summary>
    public static class DepartmentTypeCodes
    {
        public const string Production = "Production";
        public const string Maintenance = "Maintenance";
        public const string Management = "Management";
        public const string Support = "Support";
    }
}
