using MauiApp5.Shared.Services;

namespace MauiApp5.Platforms.MacCatalyst
{
    public class NotificationService : INotificationService
    {
        public async Task<bool> RequestPermissionAsync()
        {
            // MacCatalyst通知权限请求的简单实现
            return await Task.FromResult(true);
        }

        public async Task SendNotificationAsync(string title, string message)
        {
            // MacCatalyst通知发送的简单实现
            System.Diagnostics.Debug.WriteLine($"MacCatalyst通知: {title} - {message}");
            await Task.CompletedTask;
        }
    }
} 