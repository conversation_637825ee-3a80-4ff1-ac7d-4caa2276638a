@using MauiApp5.Shared.Services

<MudDialog>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="Icons.Material.Filled.Info" Class="mr-2" />
                    设备详细信息
                </MudText>
            </MudItem>

            <!-- 基本信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">基本信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备编码" Variant="Variant.Text">
                                    @EquipmentDetail.Code
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备名称" Variant="Variant.Text">
                                    @EquipmentDetail.Name
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="所属部门" Variant="Variant.Text">
                                    @EquipmentDetail.DepartmentName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备型号" Variant="Variant.Text">
                                    @EquipmentDetail.ModelName (@EquipmentDetail.ModelCategory)
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="所在位置" Variant="Variant.Text">
                                    @EquipmentDetail.LocationName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备状态" Variant="Variant.Text">
                                    <MudChip T="string" Color="@GetStatusColor(EquipmentDetail.Status)" Size="Size.Small">
                                        @EquipmentDetail.StatusName
                                    </MudChip>
                                </MudField>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 技术信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">技术信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="序列号" Variant="Variant.Text">
                                    @(EquipmentDetail.SerialNumber ?? "未设置")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="资产编号" Variant="Variant.Text">
                                    @(EquipmentDetail.AssetNumber ?? "未设置")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="品牌" Variant="Variant.Text">
                                    @(EquipmentDetail.ModelBrand ?? "未设置")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备类别" Variant="Variant.Text">
                                    @EquipmentDetail.ModelCategory
                                </MudField>
                            </MudItem>
                            @if (!string.IsNullOrEmpty(EquipmentDetail.Description))
                            {
                                <MudItem xs="12">
                                    <MudField Label="设备描述" Variant="Variant.Text">
                                        @EquipmentDetail.Description
                                    </MudField>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 时间信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">时间信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="购买日期" Variant="Variant.Text">
                                    @(EquipmentDetail.PurchaseDate?.ToString("yyyy-MM-dd") ?? "未设置")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="保修到期日期" Variant="Variant.Text">
                                    @if (EquipmentDetail.WarrantyExpiry.HasValue)
                                    {
                                        var isExpired = EquipmentDetail.WarrantyExpiry.Value < DateTime.Today;
                                        <MudText Color="@(isExpired ? Color.Error : Color.Default)">
                                            @EquipmentDetail.WarrantyExpiry.Value.ToString("yyyy-MM-dd")
                                            @if (isExpired)
                                            {
                                                <MudChip T="string" Color="Color.Error" Size="Size.Small" Class="ml-2">已过期</MudChip>
                                            }
                                        </MudText>
                                    }
                                    else
                                    {
                                        <MudText>未设置</MudText>
                                    }
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="最后维护日期" Variant="Variant.Text">
                                    @(EquipmentDetail.LastMaintenanceDate?.ToString("yyyy-MM-dd") ?? "未设置")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="下次维护日期" Variant="Variant.Text">
                                    @if (EquipmentDetail.NextMaintenanceDate.HasValue)
                                    {
                                        var isDue = EquipmentDetail.NextMaintenanceDate.Value <= DateTime.Today.AddDays(7);
                                        <MudText Color="@(isDue ? Color.Warning : Color.Default)">
                                            @EquipmentDetail.NextMaintenanceDate.Value.ToString("yyyy-MM-dd")
                                            @if (isDue)
                                            {
                                                <MudChip T="string" Color="Color.Warning" Size="Size.Small" Class="ml-2">即将到期</MudChip>
                                            }
                                        </MudText>
                                    }
                                    else
                                    {
                                        <MudText>未设置</MudText>
                                    }
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="创建时间" Variant="Variant.Text">
                                    @EquipmentDetail.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="启用状态" Variant="Variant.Text">
                                    <MudChip T="string" Color="@(EquipmentDetail.IsEnabled ? Color.Success : Color.Default)" Size="Size.Small">
                                        @(EquipmentDetail.IsEnabled ? "启用" : "禁用")
                                    </MudChip>
                                </MudField>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 统计信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">统计信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="报修次数" Variant="Variant.Text">
                                    <MudText>
                                        @EquipmentDetail.RepairOrderCount 次
                                        @if (EquipmentDetail.RepairOrderCount > 0)
                                        {
                                            <MudButton Variant="Variant.Text" 
                                                     Size="Size.Small" 
                                                     StartIcon="Icons.Material.Filled.History"
                                                     OnClick="ViewRepairHistory"
                                                     Class="ml-2">
                                                查看历史
                                            </MudButton>
                                        }
                                    </MudText>
                                </MudField>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            @if (!string.IsNullOrEmpty(EquipmentDetail.Remark))
            {
                <MudItem xs="12">
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">备注</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudText>@EquipmentDetail.Remark</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Close">关闭</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 StartIcon="Icons.Material.Filled.Build"
                 OnClick="CreateRepairOrder">
            创建报修单
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public EquipmentDetailDto EquipmentDetail { get; set; } = new();

    private Color GetStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Success,  // 正常
            2 => Color.Warning,  // 维修中
            3 => Color.Default,  // 停用
            4 => Color.Error,    // 报废
            _ => Color.Default
        };
    }

    private void Close()
    {
        MudDialog.Close();
    }

    private void CreateRepairOrder()
    {
        // TODO: 实现创建报修单功能
        MudDialog.Close(DialogResult.Ok("create_repair"));
    }

    private void ViewRepairHistory()
    {
        // TODO: 实现查看报修历史功能
        MudDialog.Close(DialogResult.Ok("view_history"));
    }
}
