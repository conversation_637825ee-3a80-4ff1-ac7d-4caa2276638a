@using MudBlazor

<MudDialog Style="max-width: 90vw; width: 900px; max-height: 80vh;">
    <DialogContent>
        <div class="d-flex flex-column" style="height: 600px;">
            <!-- 搜索框 -->
            <MudTextField @bind-Value="searchText" 
                         Label="搜索图标" 
                         Variant="Variant.Outlined" 
                         Margin="Margin.Dense"
                         AdornmentIcon="@Icons.Material.Filled.Search"
                         Adornment="Adornment.Start"
                         Class="mb-4" />
            
            <!-- 分类筛选 -->
            <MudSelect T="string" @bind-Value="selectedCategory"
                      Label="图标分类"
                      Variant="Variant.Outlined"
                      Margin="Margin.Dense"
                      Class="mb-4">
                <MudSelectItem T="string" Value="@("all")">全部</MudSelectItem>
                <MudSelectItem T="string" Value="@("common")">常用图标</MudSelectItem>
                <MudSelectItem T="string" Value="@("navigation")">导航类</MudSelectItem>
                <MudSelectItem T="string" Value="@("system")">系统管理</MudSelectItem>
                <MudSelectItem T="string" Value="@("action")">操作类</MudSelectItem>
                <MudSelectItem T="string" Value="@("content")">内容类</MudSelectItem>
                <MudSelectItem T="string" Value="@("device")">设备类</MudSelectItem>
            </MudSelect>
            
            <!-- 图标网格 -->
            <div style="flex: 1; overflow-y: auto;">
                <MudGrid Spacing="1">
                    @foreach (var icon in FilteredIcons)
                    {
                        <MudItem xs="2" sm="2" md="2" lg="1">
                            <MudPaper Class="@GetIconItemClass(icon.ConstantName)"
                                     Style="cursor: pointer; text-align: center; padding: 8px; min-height: 80px; display: flex; flex-direction: column; justify-content: center;"
                                     @onclick="() => SelectIcon(icon.ConstantName)">
                                <MudIcon Icon="@icon.SvgValue" Size="Size.Large" />
                                <MudText Typo="Typo.caption" Style="font-size: 10px; margin-top: 4px;">@icon.Name</MudText>
                            </MudPaper>
                        </MudItem>
                    }
                </MudGrid>
            </div>
            
            <!-- 预览 -->
            @if (!string.IsNullOrEmpty(selectedIcon))
            {
                <MudDivider Class="my-3" />
                <div class="d-flex align-center">
                    <MudText Class="mr-3">预览：</MudText>
                    <MudIcon Icon="@GetIconSvgValue(selectedIcon)" Size="Size.Large" />
                    <MudText Class="ml-3">@GetIconDisplayName(selectedIcon)</MudText>
                </div>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="Confirm" Disabled="string.IsNullOrEmpty(selectedIcon)">确定</MudButton>
    </DialogActions>
</MudDialog>

<style>
    .icon-item {
        transition: all 0.2s;
    }
    .icon-item:hover {
        background-color: var(--mud-palette-action-hover) !important;
        transform: scale(1.05);
    }
    .icon-item.selected {
        background-color: var(--mud-palette-primary) !important;
        color: white !important;
    }
</style>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = default!;
    [Parameter] public string? InitialValue { get; set; }

    private string searchText = "";
    private string selectedCategory = "all";
    private string selectedIcon = "";

    // 图标信息列表，返回常量名而不是SVG值
    private List<IconInfo> availableIcons = new List<IconInfo>
    {
        // 常用图标
        new("Home", "Icons.Material.Filled.Home", Icons.Material.Filled.Home, "home", "common"),
        new("Settings", "Icons.Material.Filled.Settings", Icons.Material.Filled.Settings, "settings", "common"),
        new("Search", "Icons.Material.Filled.Search", Icons.Material.Filled.Search, "search", "common"),
        new("Add", "Icons.Material.Filled.Add", Icons.Material.Filled.Add, "add", "common"),
        new("Delete", "Icons.Material.Filled.Delete", Icons.Material.Filled.Delete, "delete", "common"),
        new("Edit", "Icons.Material.Filled.Edit", Icons.Material.Filled.Edit, "edit", "common"),
        new("Refresh", "Icons.Material.Filled.Refresh", Icons.Material.Filled.Refresh, "refresh", "common"),
        new("Close", "Icons.Material.Filled.Close", Icons.Material.Filled.Close, "close", "common"),
        new("Check", "Icons.Material.Filled.Check", Icons.Material.Filled.Check, "check", "common"),
        new("Clear", "Icons.Material.Filled.Clear", Icons.Material.Filled.Clear, "clear", "common"),
        new("Info", "Icons.Material.Filled.Info", Icons.Material.Filled.Info, "info", "common"),
        new("Warning", "Icons.Material.Filled.Warning", Icons.Material.Filled.Warning, "warning", "common"),
        
        // 导航类
        new("Menu", "Icons.Material.Filled.Menu", Icons.Material.Filled.Menu, "menu", "navigation"),
        new("ArrowBack", "Icons.Material.Filled.ArrowBack", Icons.Material.Filled.ArrowBack, "arrow_back", "navigation"),
        new("ExpandMore", "Icons.Material.Filled.ExpandMore", Icons.Material.Filled.ExpandMore, "expand_more", "navigation"),
        new("ExpandLess", "Icons.Material.Filled.ExpandLess", Icons.Material.Filled.ExpandLess, "expand_less", "navigation"),
        new("ChevronLeft", "Icons.Material.Filled.ChevronLeft", Icons.Material.Filled.ChevronLeft, "chevron_left", "navigation"),
        new("ChevronRight", "Icons.Material.Filled.ChevronRight", Icons.Material.Filled.ChevronRight, "chevron_right", "navigation"),
        new("Dashboard", "Icons.Material.Filled.Dashboard", Icons.Material.Filled.Dashboard, "dashboard", "navigation"),
        new("AccountTree", "Icons.Material.Filled.AccountTree", Icons.Material.Filled.AccountTree, "account_tree", "navigation"),
        
        // 系统管理
        new("People", "Icons.Material.Filled.People", Icons.Material.Filled.People, "people", "system"),
        new("Person", "Icons.Material.Filled.Person", Icons.Material.Filled.Person, "person", "system"),
        new("PersonAdd", "Icons.Material.Filled.PersonAdd", Icons.Material.Filled.PersonAdd, "person_add", "system"),
        new("Group", "Icons.Material.Filled.Group", Icons.Material.Filled.Group, "group", "system"),
        new("Security", "Icons.Material.Filled.Security", Icons.Material.Filled.Security, "security", "system"),
        new("Lock", "Icons.Material.Filled.Lock", Icons.Material.Filled.Lock, "lock", "system"),
        new("LockOpen", "Icons.Material.Filled.LockOpen", Icons.Material.Filled.LockOpen, "lock_open", "system"),
        new("Key", "Icons.Material.Filled.Key", Icons.Material.Filled.Key, "key", "system"),
        new("Shield", "Icons.Material.Filled.Shield", Icons.Material.Filled.Shield, "shield", "system"),
        new("Admin", "Icons.Material.Filled.AdminPanelSettings", Icons.Material.Filled.AdminPanelSettings, "admin_panel_settings", "system"),
        
        // 操作类
        new("Build", "Icons.Material.Filled.Build", Icons.Material.Filled.Build, "build", "action"),
        new("Send", "Icons.Material.Filled.Send", Icons.Material.Filled.Send, "send", "action"),
        new("Save", "Icons.Material.Filled.Save", Icons.Material.Filled.Save, "save", "action"),
        new("Download", "Icons.Material.Filled.Download", Icons.Material.Filled.Download, "download", "action"),
        new("Upload", "Icons.Material.Filled.Upload", Icons.Material.Filled.Upload, "upload", "action"),
        new("Copy", "Icons.Material.Filled.FileCopy", Icons.Material.Filled.FileCopy, "file_copy", "action"),
        new("Share", "Icons.Material.Filled.Share", Icons.Material.Filled.Share, "share", "action"),
        new("Print", "Icons.Material.Filled.Print", Icons.Material.Filled.Print, "print", "action"),
        new("Visibility", "Icons.Material.Filled.Visibility", Icons.Material.Filled.Visibility, "visibility", "action"),
        new("VisibilityOff", "Icons.Material.Filled.VisibilityOff", Icons.Material.Filled.VisibilityOff, "visibility_off", "action"),
        new("Login", "Icons.Material.Filled.Login", Icons.Material.Filled.Login, "login", "action"),
        new("Logout", "Icons.Material.Filled.Logout", Icons.Material.Filled.Logout, "logout", "action"),
        new("Code", "Icons.Material.Filled.Code", Icons.Material.Filled.Code, "code", "action"),
        new("BugReport", "Icons.Material.Filled.BugReport", Icons.Material.Filled.BugReport, "bug_report", "action"),
        new("Terminal", "Icons.Material.Filled.Terminal", Icons.Material.Filled.Terminal, "terminal", "action"),
        new("AutoFixHigh", "Icons.Material.Filled.AutoFixHigh", Icons.Material.Filled.AutoFixHigh, "auto_fix_high", "action"),
        new("ClearAll", "Icons.Material.Filled.ClearAll", Icons.Material.Filled.ClearAll, "clear_all", "action"),
        new("Palette", "Icons.Material.Filled.Palette", Icons.Material.Filled.Palette, "palette", "action"),
        new("Brush", "Icons.Material.Filled.Brush", Icons.Material.Filled.Brush, "brush", "action"),
        new("ColorLens", "Icons.Material.Filled.ColorLens", Icons.Material.Filled.ColorLens, "color_lens", "action"),
        new("FormatPaint", "Icons.Material.Filled.FormatPaint", Icons.Material.Filled.FormatPaint, "format_paint", "action"),
        
        // 内容类
        new("Folder", "Icons.Material.Filled.Folder", Icons.Material.Filled.Folder, "folder", "content"),
        new("FolderOpen", "Icons.Material.Filled.FolderOpen", Icons.Material.Filled.FolderOpen, "folder_open", "content"),
        new("Description", "Icons.Material.Filled.Description", Icons.Material.Filled.Description, "description", "content"),
        new("Article", "Icons.Material.Filled.Article", Icons.Material.Filled.Article, "article", "content"),
        new("Note", "Icons.Material.Filled.Note", Icons.Material.Filled.Note, "note", "content"),
        new("Image", "Icons.Material.Filled.Image", Icons.Material.Filled.Image, "image", "content"),
        new("Video", "Icons.Material.Filled.VideoLibrary", Icons.Material.Filled.VideoLibrary, "video_library", "content"),
        new("Audio", "Icons.Material.Filled.AudioFile", Icons.Material.Filled.AudioFile, "audio_file", "content"),
        new("List", "Icons.Material.Filled.List", Icons.Material.Filled.List, "list", "content"),
        new("GridView", "Icons.Material.Filled.GridView", Icons.Material.Filled.GridView, "grid_view", "content"),
        new("TableChart", "Icons.Material.Filled.TableChart", Icons.Material.Filled.TableChart, "table_chart", "content"),
        new("Label", "Icons.Material.Filled.Label", Icons.Material.Filled.Label, "label", "content"),
        new("Tag", "Icons.Material.Filled.LocalOffer", Icons.Material.Filled.LocalOffer, "local_offer", "content"),
        new("Category", "Icons.Material.Filled.Category", Icons.Material.Filled.Category, "category", "content"),
        new("Flag", "Icons.Material.Filled.Flag", Icons.Material.Filled.Flag, "flag", "content"),
        
        // 设备/技术类
        new("QrCodeScanner", "Icons.Material.Filled.QrCodeScanner", Icons.Material.Filled.QrCodeScanner, "qr_code_scanner", "device"),
        new("Camera", "Icons.Material.Filled.CameraAlt", Icons.Material.Filled.CameraAlt, "camera_alt", "device"),
        new("Phone", "Icons.Material.Filled.Phone", Icons.Material.Filled.Phone, "phone", "device"),
        new("Computer", "Icons.Material.Filled.Computer", Icons.Material.Filled.Computer, "computer", "device"),
        new("Storage", "Icons.Material.Filled.Storage", Icons.Material.Filled.Storage, "storage", "device"),
        new("Cloud", "Icons.Material.Filled.Cloud", Icons.Material.Filled.Cloud, "cloud", "device"),
        new("Wifi", "Icons.Material.Filled.Wifi", Icons.Material.Filled.Wifi, "wifi", "device"),
        new("Bluetooth", "Icons.Material.Filled.Bluetooth", Icons.Material.Filled.Bluetooth, "bluetooth", "device"),
        new("Api", "Icons.Material.Filled.Api", Icons.Material.Filled.Api, "api", "device"),
        new("Web", "Icons.Material.Filled.Web", Icons.Material.Filled.Web, "web", "device"),
        new("WebAsset", "Icons.Material.Filled.WebAsset", Icons.Material.Filled.WebAsset, "web_asset", "device"),
        new("Architecture", "Icons.Material.Filled.Architecture", Icons.Material.Filled.Architecture, "architecture", "device"),
        new("Science", "Icons.Material.Filled.Science", Icons.Material.Filled.Science, "science", "device"),
        new("Keyboard", "Icons.Material.Filled.Keyboard", Icons.Material.Filled.Keyboard, "keyboard", "device"),
        
        // 状态指示
        new("CheckCircle", "Icons.Material.Filled.CheckCircle", Icons.Material.Filled.CheckCircle, "check_circle", "common"),
        new("Error", "Icons.Material.Filled.Error", Icons.Material.Filled.Error, "error", "common"),
        new("Cancel", "Icons.Material.Filled.Cancel", Icons.Material.Filled.Cancel, "cancel", "common"),
        new("Help", "Icons.Material.Filled.Help", Icons.Material.Filled.Help, "help", "common"),
        new("Notifications", "Icons.Material.Filled.Notifications", Icons.Material.Filled.Notifications, "notifications", "common"),
        new("NotificationsOff", "Icons.Material.Filled.NotificationsOff", Icons.Material.Filled.NotificationsOff, "notifications_off", "common"),
        
        // 时间/历史
        new("History", "Icons.Material.Filled.History", Icons.Material.Filled.History, "history", "common"),
        new("Schedule", "Icons.Material.Filled.Schedule", Icons.Material.Filled.Schedule, "schedule", "common"),
        new("AccessTime", "Icons.Material.Filled.AccessTime", Icons.Material.Filled.AccessTime, "access_time", "common"),
        new("Today", "Icons.Material.Filled.Today", Icons.Material.Filled.Today, "today", "common"),
        new("Event", "Icons.Material.Filled.Event", Icons.Material.Filled.Event, "event", "common"),
        
        // 其他常用
        new("Star", "Icons.Material.Filled.Star", Icons.Material.Filled.Star, "star", "common"),
        new("Favorite", "Icons.Material.Filled.Favorite", Icons.Material.Filled.Favorite, "favorite", "common"),
        new("Bookmark", "Icons.Material.Filled.Bookmark", Icons.Material.Filled.Bookmark, "bookmark", "common"),
        
        // 天气
        new("WbSunny", "Icons.Material.Filled.WbSunny", Icons.Material.Filled.WbSunny, "wb_sunny", "common"),
        new("Umbrella", "Icons.Material.Filled.BeachAccess", Icons.Material.Filled.BeachAccess, "beach_access", "common")
    };

    private List<IconInfo> FilteredIcons =>
        availableIcons.Where(icon =>
            (selectedCategory == "all" || icon.Category == selectedCategory) &&
            (string.IsNullOrEmpty(searchText) || 
             icon.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
             icon.Keywords.Contains(searchText, StringComparison.OrdinalIgnoreCase))
        ).ToList();

    protected override void OnInitialized()
    {
        selectedIcon = InitialValue ?? "";
    }

    void SelectIcon(string iconConstantName)
    {
        selectedIcon = iconConstantName;
    }

    void Confirm()
    {
        // 返回常量名而不是SVG值
        MudDialog?.Close(DialogResult.Ok(selectedIcon));
    }

    void Cancel()
    {
        MudDialog?.Cancel();
    }

    private string GetIconDisplayName(string iconConstantName)
    {
        var icon = availableIcons.FirstOrDefault(i => i.ConstantName == iconConstantName);
        return icon?.Name ?? iconConstantName;
    }

    private string GetIconSvgValue(string iconConstantName)
    {
        var icon = availableIcons.FirstOrDefault(i => i.ConstantName == iconConstantName);
        return icon?.SvgValue ?? Icons.Material.Filled.Circle;
    }

    private string GetIconItemClass(string iconConstantName)
    {
        return selectedIcon == iconConstantName ? "icon-item selected" : "icon-item";
    }

    public class IconInfo
    {
        public string Name { get; set; }
        public string ConstantName { get; set; } // 常量名如 "Icons.Material.Filled.Home"
        public string SvgValue { get; set; } // SVG值
        public string Keywords { get; set; }
        public string Category { get; set; }

        public IconInfo(string name, string constantName, string svgValue, string keywords, string category)
        {
            Name = name;
            ConstantName = constantName;
            SvgValue = svgValue;
            Keywords = keywords;
            Category = category;
        }
    }
}